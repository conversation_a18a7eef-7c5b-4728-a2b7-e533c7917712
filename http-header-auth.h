#ifndef _http_header_auth_h_
#define _http_header_auth_h_

#ifdef __cplusplus
extern "C" {
#endif

// Authentication scheme constants
#define HTTP_AUTHENTICATION_NONE   0
#define HTTP_AUTHENTICATION_BASIC  1
#define HTTP_AUTHENTICATION_DIGEST 2

// HTTP Authentication header parsing
struct http_header_auth_t
{
    int scheme;         // Authentication scheme (BASIC, DIGEST, etc.)
    char* realm;        // Authentication realm
    char* username;     // Username
    char* password;     // Password (for Basic auth)
    char* nonce;        // Nonce (for Digest auth)
    char* uri;          // URI (for Digest auth)
    char* response;     // Response hash (for Digest auth)
    char* algorithm;    // Algorithm (for Digest auth)
    char* cnonce;       // Client nonce (for Digest auth)
    char* nc;           // Nonce count (for Digest auth)
    char* qop;          // Quality of protection (for Digest auth)
    char* opaque;       // Opaque value (for Digest auth)
};

// WWW-Authenticate header structure
struct http_header_www_authenticate_t
{
    int scheme;         // Authentication scheme
    char realm[256];    // Authentication realm
    char nonce[256];    // Server nonce
    char algorithm[32]; // Hash algorithm
    char qop[32];       // Quality of protection
    char opaque[256];   // Opaque value
    char username[256]; // Username
    char uri[512];      // Request URI
    char response[64];  // Response hash
    char cnonce[64];    // Client nonce
    int nc;             // Nonce count
    int stale;          // Stale flag
    int userhash;       // User hash flag
};

// Parse Authorization header
int http_header_auth_parse(const char* field, struct http_header_auth_t* auth);

// Free auth structure
void http_header_auth_free(struct http_header_auth_t* auth);

// Create Basic auth header
int http_header_auth_basic(char* header, size_t bytes, const char* username, const char* password);

// Create Digest auth header
int http_header_auth_digest(char* header, size_t bytes, const char* username, const char* password,
                           const char* method, const char* uri, const char* realm, const char* nonce,
                           const char* algorithm, const char* qop, const char* cnonce, const char* nc);

// Parse WWW-Authenticate header
int http_header_www_authenticate(const char* field, struct http_header_www_authenticate_t* auth);

// Free WWW-Authenticate structure
void http_header_www_authenticate_free(struct http_header_www_authenticate_t* auth);

// Create authentication header
int http_header_auth(struct http_header_www_authenticate_t* auth, const char* pwd,
                    const char* method, const char* content, int length,
                    char* authorization, int bytes);

#ifdef __cplusplus
}
#endif

#endif /* !_http_header_auth_h_ */
