#include "sdp-options.h"
#include <string.h>
#include <stdio.h>

// Default SDP options - support most common features
const struct sdp_options_t sdp_options_default = {
    1,  // rtp_over_tcp
    1,  // multicast
    1,  // unicast
    1,  // aggregate_control
    1,  // play_notify
    1,  // record_notify
    1,  // pause_notify
    1,  // setup_notify
    1,  // teardown_notify
    1,  // get_parameter
    1,  // set_parameter
    1,  // options
    1,  // describe
    1,  // announce
    1,  // record
    0,  // redirect
};

void sdp_options_init(struct sdp_options_t* options)
{
    if (options) {
        *options = sdp_options_default;
    }
}

int sdp_options_parse(struct sdp_options_t* options, const char* str)
{
    if (!options || !str) {
        return -1;
    }
    
    // Initialize with defaults
    sdp_options_init(options);
    
    // Simple parsing - in a real implementation, this would parse
    // actual SDP options from the string
    // For now, just return success
    return 0;
}

int sdp_options_stringify(const struct sdp_options_t* options, char* str, size_t len)
{
    if (!options || !str || len == 0) {
        return -1;
    }
    
    // Simple stringification - in a real implementation, this would
    // format the options into a proper SDP string
    snprintf(str, len, "OPTIONS: TCP=%d,MC=%d,UC=%d,AC=%d",
             options->rtp_over_tcp,
             options->multicast,
             options->unicast,
             options->aggregate_control);
    
    return 0;
}
