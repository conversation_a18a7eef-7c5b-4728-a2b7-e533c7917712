#ifndef _sdp_a_fmtp_h_
#define _sdp_a_fmtp_h_

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// SDP fmtp (format parameters) attribute structure
struct sdp_fmtp_t
{
    int payload;            // Payload type number
    char *parameters;       // Format parameters string
    size_t param_len;       // Length of parameters string
};

// Parse fmtp attribute line
// Format: "a=fmtp:<payload> <parameters>"
int sdp_a_fmtp_parse(struct sdp_fmtp_t *fmtp, const char *line);

// Generate fmtp attribute line
int sdp_a_fmtp_generate(const struct sdp_fmtp_t *fmtp, char *line, size_t len);

// Create and destroy fmtp structure
struct sdp_fmtp_t* sdp_a_fmtp_create(void);
void sdp_a_fmtp_destroy(struct sdp_fmtp_t *fmtp);

// Set fmtp parameters
int sdp_a_fmtp_set(struct sdp_fmtp_t *fmtp, int payload, const char *parameters);

// Get specific parameter from fmtp
const char* sdp_a_fmtp_get_parameter(const struct sdp_fmtp_t *fmtp, const char *name);

// Set specific parameter in fmtp
int sdp_a_fmtp_set_parameter(struct sdp_fmtp_t *fmtp, const char *name, const char *value);

// Specific codec FMTP structures
struct sdp_a_fmtp_h264_t
{
    char profile_level_id[16];
    char sprop_parameter_sets[512];
};

struct sdp_a_fmtp_h265_t
{
    char profile_id[16];
    char sprop_vps[256];
    char sprop_sps[256];
    char sprop_pps[256];
    char sprop_sei[256];
};

struct sdp_a_fmtp_h266_t
{
    char profile_id[16];
    char sprop_vps[256];
    char sprop_sps[256];
    char sprop_pps[256];
    char sprop_sei[256];
};

struct sdp_a_fmtp_mpeg4_t
{
    char config[512];
};

// Common fmtp parameter parsing for specific codecs
int sdp_a_fmtp_h264(struct sdp_fmtp_t *fmtp, const char *profile_level_id, const char *sprop_parameter_sets);
int sdp_a_fmtp_h265(struct sdp_fmtp_t *fmtp, const char *profile_id, const char *sprop_vps);
int sdp_a_fmtp_aac(struct sdp_fmtp_t *fmtp, const char *config);

#ifdef __cplusplus
}
#endif

#endif /* !_sdp_a_fmtp_h_ */
