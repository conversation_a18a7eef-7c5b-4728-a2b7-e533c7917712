#ifndef _sdp_payload_h_
#define _sdp_payload_h_

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// SDP payload structure
struct sdp_payload_t
{
    int payload;            // Payload type number
    char encoding[32];      // Encoding name
    int clock_rate;         // Clock rate in Hz
    int channels;           // Number of channels (audio only)
    char *fmtp;            // Format parameters
    size_t fmtp_len;       // Length of format parameters
};

// SDP media payload collection
struct sdp_media_payload_t
{
    struct sdp_payload_t *payloads;
    int payload_count;
    int payload_capacity;
};

// Create and destroy payload structures
struct sdp_payload_t* sdp_payload_create(void);
void sdp_payload_destroy(struct sdp_payload_t* payload);

struct sdp_media_payload_t* sdp_media_payload_create(void);
void sdp_media_payload_destroy(struct sdp_media_payload_t* media_payload);

// Payload management
int sdp_payload_set(struct sdp_payload_t* payload, int pt, const char* encoding, int clock_rate, int channels);
int sdp_payload_set_fmtp(struct sdp_payload_t* payload, const char* fmtp);

// Media payload management
int sdp_media_payload_add(struct sdp_media_payload_t* media_payload, const struct sdp_payload_t* payload);
struct sdp_payload_t* sdp_media_payload_find(const struct sdp_media_payload_t* media_payload, int payload_type);
struct sdp_payload_t* sdp_media_payload_find_by_encoding(const struct sdp_media_payload_t* media_payload, const char* encoding);

// Payload parsing and generation
int sdp_payload_parse_rtpmap(struct sdp_payload_t* payload, const char* rtpmap);
int sdp_payload_parse_fmtp(struct sdp_payload_t* payload, const char* fmtp);

int sdp_payload_generate_rtpmap(const struct sdp_payload_t* payload, char* rtpmap, size_t len);
int sdp_payload_generate_fmtp(const struct sdp_payload_t* payload, char* fmtp, size_t len);

// Common payload configurations
int sdp_payload_h264(struct sdp_payload_t* payload, int pt, const char* profile_level_id, const char* sprop_parameter_sets);
int sdp_payload_h265(struct sdp_payload_t* payload, int pt, const char* profile_id, const char* sprop_vps);
int sdp_payload_aac(struct sdp_payload_t* payload, int pt, int clock_rate, int channels, const char* config);
int sdp_payload_opus(struct sdp_payload_t* payload, int pt, int clock_rate, int channels);
int sdp_payload_pcmu(struct sdp_payload_t* payload);
int sdp_payload_pcma(struct sdp_payload_t* payload);
int sdp_payload_g722(struct sdp_payload_t* payload);
int sdp_payload_g729(struct sdp_payload_t* payload);

// Payload type constants (from RTP profile)
#define SDP_PAYLOAD_PCMU        0
#define SDP_PAYLOAD_PCMA        8
#define SDP_PAYLOAD_G722        9
#define SDP_PAYLOAD_G729        18
#define SDP_PAYLOAD_H264        96
#define SDP_PAYLOAD_H265        97
#define SDP_PAYLOAD_AAC         98
#define SDP_PAYLOAD_OPUS        99

#ifdef __cplusplus
}
#endif

#endif /* !_sdp_payload_h_ */
