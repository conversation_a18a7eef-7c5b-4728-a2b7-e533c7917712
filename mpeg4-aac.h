#ifndef _mpeg4_aac_h_
#define _mpeg4_aac_h_

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// MPEG-4 AAC Profile constants
#define MPEG4_AAC_MAIN      1
#define MPEG4_AAC_LC        2
#define MPEG4_AAC_SSR       3
#define MPEG4_AAC_LTP       4
#define MPEG4_AAC_HE        5
#define MPEG4_AAC_HE_V2     29

// MPEG-4 AAC structure
struct mpeg4_aac_t
{
    uint8_t profile;                    // Audio Object Type
    uint8_t sampling_frequency_index;   // Sampling frequency index
    uint8_t channel_configuration;      // Channel configuration
    uint8_t frame_length_flag;          // Frame length flag (0=1024, 1=960)
    uint8_t depends_on_core_coder;      // Depends on core coder flag
    uint8_t core_coder_delay;           // Core coder delay
    uint8_t extension_flag;             // Extension flag
    uint8_t extension_flag3;            // Extension flag 3
    
    // SBR (Spectral Band Replication) parameters
    uint8_t sbr_present_flag;           // SBR present flag
    uint8_t ps_present_flag;            // PS (Parametric Stereo) present flag
    uint8_t extension_sampling_frequency_index; // Extension sampling frequency index
    
    // Additional parameters
    uint32_t sampling_frequency;        // Actual sampling frequency
    uint32_t extension_sampling_frequency; // Extension sampling frequency
    uint8_t channels;                   // Number of channels
};

// Audio frequency mapping functions
int mpeg4_aac_audio_frequency_from(uint32_t frequency);
uint32_t mpeg4_aac_audio_frequency_to(uint8_t index);

// Profile level functions
int mpeg4_aac_profile_level(const struct mpeg4_aac_t* aac);

// Audio Specific Config functions
int mpeg4_aac_audio_specific_config_load(const uint8_t* data, size_t bytes, struct mpeg4_aac_t* aac);
int mpeg4_aac_audio_specific_config_save(const struct mpeg4_aac_t* aac, uint8_t* data, size_t bytes);

// Stream Mux Config functions
int mpeg4_aac_stream_mux_config_load(const uint8_t* data, size_t bytes, struct mpeg4_aac_t* aac);
int mpeg4_aac_stream_mux_config_save(const struct mpeg4_aac_t* aac, uint8_t* data, size_t bytes);

// ADTS (Audio Data Transport Stream) functions
int mpeg4_aac_adts_load(const uint8_t* data, size_t bytes, struct mpeg4_aac_t* aac);
int mpeg4_aac_adts_save(const struct mpeg4_aac_t* aac, size_t payload, uint8_t* data, size_t bytes);

// Utility functions
int mpeg4_aac_adts_frame_length(const uint8_t* data, size_t bytes);
int mpeg4_aac_adts_frames(const void* data, size_t bytes);

#ifdef __cplusplus
}
#endif

#endif /* !_mpeg4_aac_h_ */
