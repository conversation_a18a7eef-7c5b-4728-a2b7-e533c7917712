#ifndef _rtsp_utils_h_
#define _rtsp_utils_h_

#include <stdint.h>
#include <stddef.h>
#include <string>
#include <memory>

#ifdef __cplusplus
extern "C" {
#endif

// Basic type definitions
#ifndef SOCKET_ADDRLEN
#define SOCKET_ADDRLEN 64
#endif

// String utility functions
int strstartswith(const char* str, const char* prefix);
int strendswith(const char* str, const char* suffix);
const char* path_basename(const char* path);
uint64_t ntp64_now(void);
uint64_t system_time(void);

// URI parsing structures and functions
struct uri_t {
    char* scheme;
    char* host;
    int port;
    char* path;
    char* query;
    char* fragment;
};

struct uri_t* uri_parse(const char* uri, size_t len);
void uri_free(struct uri_t* uri);
int url_decode(const char* src, size_t srclen, char* dst, size_t dstlen);

#ifdef __cplusplus
}

// C++ classes and utilities
class ThreadLocker {
public:
    ThreadLocker();
    ~ThreadLocker();
    void Lock();
    void Unlock();
private:
    void* m_mutex;
};

class AutoThreadLocker {
public:
    AutoThreadLocker(ThreadLocker& locker);
    ~AutoThreadLocker();
private:
    ThreadLocker& m_locker;
};

// Path operations namespace
namespace path {
    std::string join(const char* path1, const char* path2);
}

#endif

#endif /* !_rtsp_utils_h_ */