#ifndef _locker_h_
#define _locker_h_

#ifdef __cplusplus
extern "C" {
#endif

#if defined(_WIN32)
#include <windows.h>
typedef CRITICAL_SECTION locker_t;
#else
#include <pthread.h>
typedef pthread_mutex_t locker_t;
#endif

// Locker functions
int locker_create(locker_t* locker);
int locker_destroy(locker_t* locker);
int locker_lock(locker_t* locker);
int locker_unlock(locker_t* locker);
int locker_trylock(locker_t* locker);

#ifdef __cplusplus
}
#endif

#endif /* !_locker_h_ */
