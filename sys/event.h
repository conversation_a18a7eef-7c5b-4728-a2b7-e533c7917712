#ifndef _event_h_
#define _event_h_

#ifdef __cplusplus
extern "C" {
#endif

#if defined(_WIN32)
#include <windows.h>
typedef HANDLE event_t;
#else
#include <pthread.h>
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int signaled;
} event_t;
#endif

// Event functions
int event_create(event_t* event);
int event_destroy(event_t* event);
int event_wait(event_t* event);
int event_timewait(event_t* event, int timeout);
int event_signal(event_t* event);
int event_reset(event_t* event);

#ifdef __cplusplus
}
#endif

#endif /* !_event_h_ */
