#ifndef _ntp_time_h_
#define _ntp_time_h_

#include <stdint.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

// NTP timestamp format:
// 32-bit seconds since 1900-01-01 00:00:00 UTC
// 32-bit fractional seconds (2^32 = 1 second)

// Convert system time to NTP timestamp
uint64_t ntp64_now(void);

// Convert NTP timestamp to system time (microseconds since epoch)
uint64_t ntp2clock(uint64_t ntp);

// Convert system time (microseconds since epoch) to NTP timestamp
uint64_t clock2ntp(uint64_t clock);

// Get current system time in microseconds since epoch
uint64_t system_time(void);

#ifdef __cplusplus
}
#endif

#endif /* !_ntp_time_h_ */
