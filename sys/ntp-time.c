#include "ntp-time.h"
#include <sys/time.h>
#include <time.h>

// NTP epoch starts at 1900-01-01 00:00:00 UTC
// Unix epoch starts at 1970-01-01 00:00:00 UTC
// Difference is 70 years = 2208988800 seconds
#define NTP_UNIX_EPOCH_DIFF 2208988800ULL

uint64_t system_time(void)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000000 + tv.tv_usec;
}

uint64_t ntp64_now(void)
{
    return clock2ntp(system_time());
}

uint64_t clock2ntp(uint64_t clock)
{
    uint64_t ntp;
    
    // Convert microseconds to seconds and add NTP epoch offset
    uint64_t seconds = clock / 1000000 + NTP_UNIX_EPOCH_DIFF;
    uint64_t microseconds = clock % 1000000;
    
    // High 32 bits: seconds since NTP epoch
    ntp = seconds << 32;
    
    // Low 32 bits: fractional seconds
    // Convert microseconds to NTP fractional format
    // microseconds * 2^32 / 10^6
    ntp |= (uint32_t)((microseconds << 26) / 15625);
    
    return ntp;
}

uint64_t ntp2clock(uint64_t ntp)
{
    uint64_t clock;
    
    // Extract seconds and convert from NTP epoch to Unix epoch
    uint32_t seconds = (uint32_t)(ntp >> 32);
    uint32_t fraction = (uint32_t)(ntp & 0xFFFFFFFF);
    
    // Convert to Unix timestamp
    clock = ((uint64_t)(seconds - NTP_UNIX_EPOCH_DIFF)) * 1000000;
    
    // Convert fractional part to microseconds
    // fraction * 10^6 / 2^32
    clock += ((uint64_t)fraction * 15625) >> 26;
    
    return clock;
}
