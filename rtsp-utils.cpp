#include "rtsp-utils.h"
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <time.h>
#include <assert.h>

#ifdef _WIN32
#include <windows.h>
#include <winsock2.h>
#define PATH_SEPARATOR '\\'
#else
#include <pthread.h>
#include <sys/time.h>
#include <unistd.h>
#define PATH_SEPARATOR '/'
#endif

// String utility functions implementation
int strstartswith(const char* str, const char* prefix)
{
    if (!str || !prefix) return 0;
    size_t str_len = strlen(str);
    size_t prefix_len = strlen(prefix);
    if (prefix_len > str_len) return 0;
    return strncmp(str, prefix, prefix_len) == 0;
}

int strendswith(const char* str, const char* suffix)
{
    if (!str || !suffix) return 0;
    size_t str_len = strlen(str);
    size_t suffix_len = strlen(suffix);
    if (suffix_len > str_len) return 0;
    return strcmp(str + str_len - suffix_len, suffix) == 0;
}

const char* path_basename(const char* path)
{
    if (!path) return NULL;
    const char* last_sep = strrchr(path, PATH_SEPARATOR);
#ifdef _WIN32
    // Windows also supports forward slash
    const char* last_fwd = strrchr(path, '/');
    if (last_fwd && (!last_sep || last_fwd > last_sep))
        last_sep = last_fwd;
#endif
    return last_sep ? last_sep + 1 : path;
}

// Time functions
uint64_t ntp64_now(void)
{
    // NTP epoch starts at 1900-01-01, Unix epoch at 1970-01-01
    // Difference is 70 years = 2208988800 seconds
    const uint64_t NTP_UNIX_OFFSET = 2208988800ULL;
    
#ifdef _WIN32
    FILETIME ft;
    GetSystemTimeAsFileTime(&ft);
    uint64_t time_100ns = ((uint64_t)ft.dwHighDateTime << 32) | ft.dwLowDateTime;
    // Convert from 100ns intervals since 1601 to seconds since 1900
    uint64_t unix_time = (time_100ns / 10000000ULL) - 11644473600ULL;
    return (unix_time + NTP_UNIX_OFFSET) << 32;
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    uint64_t seconds = tv.tv_sec + NTP_UNIX_OFFSET;
    uint64_t fraction = ((uint64_t)tv.tv_usec << 32) / 1000000ULL;
    return (seconds << 32) | fraction;
#endif
}

uint64_t system_time(void)
{
#ifdef _WIN32
    return GetTickCount64();
#else
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000ULL + ts.tv_nsec / 1000000ULL;
#endif
}

// Simple URI parsing implementation
struct uri_t* uri_parse(const char* uri, size_t len)
{
    if (!uri || len == 0) return NULL;
    
    struct uri_t* result = (struct uri_t*)calloc(1, sizeof(struct uri_t));
    if (!result) return NULL;
    
    char* uri_copy = (char*)malloc(len + 1);
    if (!uri_copy) {
        free(result);
        return NULL;
    }
    
    memcpy(uri_copy, uri, len);
    uri_copy[len] = '\0';
    
    // Parse scheme
    char* colon = strchr(uri_copy, ':');
    if (colon) {
        *colon = '\0';
        result->scheme = strdup(uri_copy);
        uri_copy = colon + 1;
        
        // Skip "//"
        if (strncmp(uri_copy, "//", 2) == 0) {
            uri_copy += 2;
        }
    }
    
    // Parse host and port
    char* slash = strchr(uri_copy, '/');
    char* host_end = slash ? slash : uri_copy + strlen(uri_copy);
    
    char* port_start = strchr(uri_copy, ':');
    if (port_start && port_start < host_end) {
        *port_start = '\0';
        result->port = atoi(port_start + 1);
    } else {
        result->port = 0;
    }
    
    if (uri_copy < host_end) {
        size_t host_len = (port_start && port_start < host_end) ? 
                         (port_start - uri_copy) : (host_end - uri_copy);
        result->host = (char*)malloc(host_len + 1);
        if (result->host) {
            memcpy(result->host, uri_copy, host_len);
            result->host[host_len] = '\0';
        }
    }
    
    // Parse path
    if (slash) {
        char* query_start = strchr(slash, '?');
        if (query_start) {
            size_t path_len = query_start - slash;
            result->path = (char*)malloc(path_len + 1);
            if (result->path) {
                memcpy(result->path, slash, path_len);
                result->path[path_len] = '\0';
            }
            
            // Parse query
            char* fragment_start = strchr(query_start + 1, '#');
            if (fragment_start) {
                size_t query_len = fragment_start - query_start - 1;
                result->query = (char*)malloc(query_len + 1);
                if (result->query) {
                    memcpy(result->query, query_start + 1, query_len);
                    result->query[query_len] = '\0';
                }
                result->fragment = strdup(fragment_start + 1);
            } else {
                result->query = strdup(query_start + 1);
            }
        } else {
            result->path = strdup(slash);
        }
    }
    
    free(uri_copy - (colon ? strlen(result->scheme) + 1 : 0) - 
          (strncmp(uri, "//", 2) == 0 ? 2 : 0));
    return result;
}

void uri_free(struct uri_t* uri)
{
    if (!uri) return;
    free(uri->scheme);
    free(uri->host);
    free(uri->path);
    free(uri->query);
    free(uri->fragment);
    free(uri);
}

// URL decode implementation
int url_decode(const char* src, size_t srclen, char* dst, size_t dstlen)
{
    if (!src || !dst || dstlen == 0) return -1;
    
    size_t i = 0, j = 0;
    while (i < srclen && j < dstlen - 1) {
        if (src[i] == '%' && i + 2 < srclen) {
            int high = 0, low = 0;
            char c1 = src[i + 1], c2 = src[i + 2];
            
            if (c1 >= '0' && c1 <= '9') high = c1 - '0';
            else if (c1 >= 'A' && c1 <= 'F') high = c1 - 'A' + 10;
            else if (c1 >= 'a' && c1 <= 'f') high = c1 - 'a' + 10;
            else { dst[j++] = src[i++]; continue; }
            
            if (c2 >= '0' && c2 <= '9') low = c2 - '0';
            else if (c2 >= 'A' && c2 <= 'F') low = c2 - 'A' + 10;
            else if (c2 >= 'a' && c2 <= 'f') low = c2 - 'a' + 10;
            else { dst[j++] = src[i++]; continue; }
            
            dst[j++] = (high << 4) | low;
            i += 3;
        } else if (src[i] == '+') {
            dst[j++] = ' ';
            i++;
        } else {
            dst[j++] = src[i++];
        }
    }
    dst[j] = '\0';
    return j;
}

// ThreadLocker implementation
ThreadLocker::ThreadLocker()
{
#ifdef _WIN32
    m_mutex = malloc(sizeof(CRITICAL_SECTION));
    InitializeCriticalSection((CRITICAL_SECTION*)m_mutex);
#else
    m_mutex = malloc(sizeof(pthread_mutex_t));
    pthread_mutex_init((pthread_mutex_t*)m_mutex, NULL);
#endif
}

ThreadLocker::~ThreadLocker()
{
    if (m_mutex) {
#ifdef _WIN32
        DeleteCriticalSection((CRITICAL_SECTION*)m_mutex);
#else
        pthread_mutex_destroy((pthread_mutex_t*)m_mutex);
#endif
        free(m_mutex);
    }
}

void ThreadLocker::Lock()
{
    if (m_mutex) {
#ifdef _WIN32
        EnterCriticalSection((CRITICAL_SECTION*)m_mutex);
#else
        pthread_mutex_lock((pthread_mutex_t*)m_mutex);
#endif
    }
}

void ThreadLocker::Unlock()
{
    if (m_mutex) {
#ifdef _WIN32
        LeaveCriticalSection((CRITICAL_SECTION*)m_mutex);
#else
        pthread_mutex_unlock((pthread_mutex_t*)m_mutex);
#endif
    }
}

// AutoThreadLocker implementation
AutoThreadLocker::AutoThreadLocker(ThreadLocker& locker) : m_locker(locker)
{
    m_locker.Lock();
}

AutoThreadLocker::~AutoThreadLocker()
{
    m_locker.Unlock();
}

// Path operations
namespace path {
    std::string join(const char* path1, const char* path2)
    {
        if (!path1 || !path2) return "";

        std::string result = path1;
        if (!result.empty() && result.back() != PATH_SEPARATOR && result.back() != '/') {
            result += PATH_SEPARATOR;
        }

        // Skip leading separators in path2
        while (*path2 == PATH_SEPARATOR || *path2 == '/') {
            path2++;
        }

        result += path2;
        return result;
    }
}

// Utility functions for media sources are now in separate files


