#include "rtp-tcp-transport.h"
#include "rtsp-server.h"
#include <assert.h>
#include <string.h>

RTPTcpTransport::RTPTcpTransport(void* rtsp, int rtp_channel, int rtcp_channel)
    : m_rtsp(rtsp)
    , m_rtp_channel(rtp_channel)
    , m_rtcp_channel(rtcp_channel)
{
}

RTPTcpTransport::~RTPTcpTransport()
{
}

int RTPTcpTransport::Send(bool rtcp, const void* data, size_t bytes)
{
    if (!m_rtsp || !data || bytes == 0)
        return -1;

    // RTP over RTSP (RFC 2326 Section 10.12)
    // Interleaved binary data format:
    // $<channel><length><data>
    
    int channel = rtcp ? m_rtcp_channel : m_rtp_channel;
    
    // Create interleaved frame header
    unsigned char header[4];
    header[0] = '$';                    // Magic byte
    header[1] = (unsigned char)channel; // Channel number
    header[2] = (unsigned char)(bytes >> 8);   // Length high byte
    header[3] = (unsigned char)(bytes & 0xFF); // Length low byte
    
    // Send header first
    rtsp_server_t* rtsp = (rtsp_server_t*)m_rtsp;
    int ret = rtsp_server_send_interleaved_data(rtsp, channel, data, bytes);
    
    return ret;
}

// Alternative implementation if rtsp_server_send_interleaved_data is not available
int RTPTcpTransport::SendRaw(bool rtcp, const void* data, size_t bytes)
{
    if (!m_rtsp || !data || bytes == 0)
        return -1;

    int channel = rtcp ? m_rtcp_channel : m_rtp_channel;
    
    // Create complete interleaved frame
    size_t total_size = 4 + bytes;
    unsigned char* frame = new unsigned char[total_size];
    
    // Build frame header
    frame[0] = '$';                    // Magic byte
    frame[1] = (unsigned char)channel; // Channel number
    frame[2] = (unsigned char)(bytes >> 8);   // Length high byte
    frame[3] = (unsigned char)(bytes & 0xFF); // Length low byte
    
    // Copy data
    memcpy(frame + 4, data, bytes);
    
    // Send complete frame
    // This would need to use the raw socket send function
    // For now, we'll use a placeholder
    int ret = 0; // TODO: Implement raw socket send
    
    delete[] frame;
    return ret;
}
