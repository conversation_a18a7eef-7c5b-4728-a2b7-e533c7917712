#ifndef _mpeg4_vvc_h_
#define _mpeg4_vvc_h_

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// VVC (Versatile Video Coding) Profile constants
#define VVC_PROFILE_MAIN10              1
#define VVC_PROFILE_MAIN12              2
#define VVC_PROFILE_MAIN_444_10         3
#define VVC_PROFILE_MAIN_444_12         4
#define VVC_PROFILE_MAIN_444_16_INTRA   5
#define VVC_PROFILE_MAIN_444_16_STILL   6

// VVC Level constants
#define VVC_LEVEL_1     16
#define VVC_LEVEL_2     32
#define VVC_LEVEL_2_1   35
#define VVC_LEVEL_3     48
#define VVC_LEVEL_3_1   51
#define VVC_LEVEL_4     64
#define VVC_LEVEL_4_1   67
#define VVC_LEVEL_5     80
#define VVC_LEVEL_5_1   83
#define VVC_LEVEL_5_2   86
#define VVC_LEVEL_6     96
#define VVC_LEVEL_6_1   99
#define VVC_LEVEL_6_2   102

// VVC NALU types
enum vvc_nalu_type_t
{
    VVC_NALU_TRAIL = 0,
    VVC_NALU_STSA = 1,
    VVC_NALU_RADL = 2,
    VVC_NALU_RASL = 3,
    VVC_NALU_RSV_VCL_4 = 4,
    VVC_NALU_RSV_VCL_5 = 5,
    VVC_NALU_RSV_VCL_6 = 6,
    VVC_NALU_IDR_W_RADL = 7,
    VVC_NALU_IDR_N_LP = 8,
    VVC_NALU_CRA = 9,
    VVC_NALU_GDR = 10,
    VVC_NALU_RSV_IRAP_11 = 11,
    VVC_NALU_OPI = 12,
    VVC_NALU_DCI = 13,
    VVC_NALU_VPS = 14,
    VVC_NALU_SPS = 15,
    VVC_NALU_PPS = 16,
    VVC_NALU_PREFIX_APS = 17,
    VVC_NALU_SUFFIX_APS = 18,
    VVC_NALU_PH = 19,
    VVC_NALU_AUD = 20,
    VVC_NALU_EOS = 21,
    VVC_NALU_EOB = 22,
    VVC_NALU_PREFIX_SEI = 23,
    VVC_NALU_SUFFIX_SEI = 24,
    VVC_NALU_FD = 25,
    VVC_NALU_RSV_NVCL_26 = 26,
    VVC_NALU_RSV_NVCL_27 = 27,
    VVC_NALU_UNSPEC_28 = 28,
    VVC_NALU_UNSPEC_29 = 29,
    VVC_NALU_UNSPEC_30 = 30,
    VVC_NALU_UNSPEC_31 = 31,
};

// VVC VPS (Video Parameter Set) structure
struct vvc_vps_t
{
    uint8_t vps_video_parameter_set_id;
    uint8_t vps_max_layers_minus1;
    uint8_t vps_max_sublayers_minus1;
    uint8_t vps_default_ptl_dpb_hrd_max_tid_flag;
    uint8_t vps_all_independent_layers_flag;
};

// VVC SPS (Sequence Parameter Set) structure
struct vvc_sps_t
{
    uint8_t sps_seq_parameter_set_id;
    uint8_t sps_video_parameter_set_id;
    uint8_t sps_max_sublayers_minus1;
    uint8_t sps_chroma_format_idc;
    uint8_t sps_log2_ctu_size_minus5;
    uint8_t sps_ptl_dpb_hrd_params_present_flag;
    uint32_t sps_pic_width_max_in_luma_samples;
    uint32_t sps_pic_height_max_in_luma_samples;
    uint8_t sps_conformance_window_flag;
    uint8_t sps_bit_depth_minus8;
};

// VVC PPS (Picture Parameter Set) structure
struct vvc_pps_t
{
    uint8_t pps_pic_parameter_set_id;
    uint8_t pps_seq_parameter_set_id;
    uint8_t pps_mixed_nalu_types_in_pic_flag;
    uint32_t pps_pic_width_in_luma_samples;
    uint32_t pps_pic_height_in_luma_samples;
    uint8_t pps_conformance_window_flag;
    uint8_t pps_scaling_window_explicit_signalling_flag;
};

// VVC decoder configuration record
struct mpeg4_vvc_t
{
    uint8_t configuration_version;
    uint8_t general_profile_idc;
    uint8_t general_tier_flag;
    uint8_t general_level_idc;
    uint8_t ptl_frame_only_constraint_flag;
    uint8_t ptl_multilayer_enabled_flag;
    uint8_t general_constraint_info[12];
    uint8_t ptl_sublayer_present_mask;
    uint16_t ptl_num_sub_profiles;
    uint32_t *general_sub_profile_idc;
    uint16_t max_picture_width;
    uint16_t max_picture_height;
    uint16_t avg_frame_rate;
    uint8_t constant_frame_rate;
    uint8_t num_temporal_layers;
    uint8_t temporal_id_nested;
    uint8_t length_size_minus_one;
    uint8_t ptl_present_flag;
    
    // Parameter sets
    uint8_t num_of_arrays;
    struct {
        uint8_t array_completeness;
        uint8_t nal_unit_type;
        uint16_t num_nalus;
        struct {
            uint16_t nal_unit_length;
            uint8_t *nal_unit;
        } *nalus;
    } *arrays;
};

// VVC parsing functions
int mpeg4_vvc_decoder_configuration_record_load(const uint8_t* data, size_t bytes, struct mpeg4_vvc_t* vvc);
int mpeg4_vvc_decoder_configuration_record_save(const struct mpeg4_vvc_t* vvc, uint8_t* data, size_t bytes);

// VVC parameter set parsing
int mpeg4_vvc_vps_parse(const uint8_t* data, size_t bytes, struct vvc_vps_t* vps);
int mpeg4_vvc_sps_parse(const uint8_t* data, size_t bytes, struct vvc_sps_t* sps);
int mpeg4_vvc_pps_parse(const uint8_t* data, size_t bytes, struct vvc_pps_t* pps);

// Utility functions
int mpeg4_vvc_profile_level(const struct mpeg4_vvc_t* vvc);
enum vvc_nalu_type_t mpeg4_vvc_nalu_type(const uint8_t* nalu);

// Memory management
struct mpeg4_vvc_t* mpeg4_vvc_create(void);
void mpeg4_vvc_destroy(struct mpeg4_vvc_t* vvc);

#ifdef __cplusplus
}
#endif

#endif /* !_mpeg4_vvc_h_ */
