#include "h265-file-reader.h"
#include <string.h>
#include <assert.h>

H265FileReader::H265FileReader(const char* file)
{
    m_ptr = NULL;
    m_capacity = 0;
    m_duration = 0;
    
    FILE* fp = fopen(file, "rb");
    if (!fp) return;
    
    // Get file size
    fseek(fp, 0, SEEK_END);
    long filesize = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    
    if (filesize <= 0) {
        fclose(fp);
        return;
    }
    
    // Allocate buffer
    m_capacity = filesize;
    m_ptr = (uint8_t*)malloc(m_capacity);
    if (!m_ptr) {
        fclose(fp);
        return;
    }
    
    // Read entire file
    size_t n = fread(m_ptr, 1, m_capacity, fp);
    fclose(fp);
    
    if (n != m_capacity) {
        free(m_ptr);
        m_ptr = NULL;
        m_capacity = 0;
        return;
    }
    
    Init();
}

H265FileReader::~H265FileReader()
{
    if (m_ptr) {
        free(m_ptr);
        m_ptr = NULL;
    }
}

bool H265FileReader::IsOpened() const
{
    return m_ptr != NULL && !m_videos.empty();
}

int H265FileReader::Init()
{
    if (!m_ptr || m_capacity < 4) return -1;
    
    // Simple H265 NALU parsing
    // Look for start codes (0x00000001 or 0x000001)
    size_t i = 0;
    int64_t timestamp = 0;
    const int frame_duration = 40; // 25fps = 40ms per frame
    
    while (i < m_capacity - 4) {
        // Look for start code
        if ((m_ptr[i] == 0x00 && m_ptr[i+1] == 0x00 && m_ptr[i+2] == 0x00 && m_ptr[i+3] == 0x01) ||
            (m_ptr[i] == 0x00 && m_ptr[i+1] == 0x00 && m_ptr[i+2] == 0x01)) {
            
            size_t start_code_len = (m_ptr[i+2] == 0x01) ? 3 : 4;
            size_t nalu_start = i + start_code_len;
            
            if (nalu_start >= m_capacity) break;
            
            // Find next start code to determine NALU length
            size_t j = nalu_start + 1;
            while (j < m_capacity - 4) {
                if ((m_ptr[j] == 0x00 && m_ptr[j+1] == 0x00 && m_ptr[j+2] == 0x00 && m_ptr[j+3] == 0x01) ||
                    (m_ptr[j] == 0x00 && m_ptr[j+1] == 0x00 && m_ptr[j+2] == 0x01)) {
                    break;
                }
                j++;
            }
            
            size_t nalu_len = (j < m_capacity) ? (j - nalu_start) : (m_capacity - nalu_start);
            
            if (nalu_len > 0) {
                uint8_t nalu_type = (m_ptr[nalu_start] >> 1) & 0x3F;
                
                // H265 NALU types
                bool is_parameter_set = (nalu_type == 32 || nalu_type == 33 || nalu_type == 34); // VPS, SPS, PPS
                bool is_idr = (nalu_type >= 19 && nalu_type <= 20); // IDR frames
                bool is_slice = (nalu_type <= 9); // Various slice types
                
                if (is_parameter_set) {
                    // Store parameter sets (VPS, SPS, PPS)
                    m_vps.push_back(std::make_pair(m_ptr + nalu_start, nalu_len));
                } else if (is_slice || is_idr) {
                    // Store frame
                    vframe_t frame;
                    frame.nalu = m_ptr + nalu_start;
                    frame.bytes = nalu_len;
                    frame.time = timestamp;
                    frame.idr = is_idr;
                    
                    m_videos.push_back(frame);
                    timestamp += frame_duration;
                }
            }
            
            i = j;
        } else {
            i++;
        }
    }
    
    if (!m_videos.empty()) {
        m_duration = m_videos.back().time + frame_duration;
        m_vit = m_videos.begin();
    }
    
    return m_videos.empty() ? -1 : 0;
}

int H265FileReader::GetNextFrame(int64_t &dts, const uint8_t* &ptr, size_t &bytes)
{
    if (m_vit == m_videos.end()) {
        // Loop back to beginning
        m_vit = m_videos.begin();
    }
    
    if (m_vit == m_videos.end()) {
        return -1; // No frames
    }
    
    dts = m_vit->time;
    ptr = m_vit->nalu;
    bytes = m_vit->bytes;
    
    ++m_vit;
    return 0;
}

int H265FileReader::Seek(int64_t &dts)
{
    // Find frame closest to requested time
    vframes_t::iterator best = m_videos.begin();
    for (vframes_t::iterator it = m_videos.begin(); it != m_videos.end(); ++it) {
        if (it->time <= dts) {
            best = it;
        } else {
            break;
        }
    }
    
    m_vit = best;
    if (m_vit != m_videos.end()) {
        dts = m_vit->time;
        return 0;
    }
    
    return -1;
}
