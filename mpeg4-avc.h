#ifndef _mpeg4_avc_h_
#define _mpeg4_avc_h_

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// AVC Profile constants
#define AVC_PROFILE_BASELINE    66
#define AVC_PROFILE_MAIN        77
#define AVC_PROFILE_EXTENDED    88
#define AVC_PROFILE_HIGH        100
#define AVC_PROFILE_HIGH10      110
#define AVC_PROFILE_HIGH422     122
#define AVC_PROFILE_HIGH444     144

// AVC Level constants
#define AVC_LEVEL_1     10
#define AVC_LEVEL_1_1   11
#define AVC_LEVEL_1_2   12
#define AVC_LEVEL_1_3   13
#define AVC_LEVEL_2     20
#define AVC_LEVEL_2_1   21
#define AVC_LEVEL_2_2   22
#define AVC_LEVEL_3     30
#define AVC_LEVEL_3_1   31
#define AVC_LEVEL_3_2   32
#define AVC_LEVEL_4     40
#define AVC_LEVEL_4_1   41
#define AVC_LEVEL_4_2   42
#define AVC_LEVEL_5     50
#define AVC_LEVEL_5_1   51
#define AVC_LEVEL_5_2   52
#define AVC_LEVEL_6     60
#define AVC_LEVEL_6_1   61
#define AVC_LEVEL_6_2   62

// AVC NALU types
enum avc_nalu_type_t
{
    AVC_NALU_UNSPECIFIED = 0,
    AVC_NALU_NON_IDR = 1,
    AVC_NALU_PARTITION_A = 2,
    AVC_NALU_PARTITION_B = 3,
    AVC_NALU_PARTITION_C = 4,
    AVC_NALU_IDR = 5,
    AVC_NALU_SEI = 6,
    AVC_NALU_SPS = 7,
    AVC_NALU_PPS = 8,
    AVC_NALU_AUD = 9,
    AVC_NALU_END_OF_SEQUENCE = 10,
    AVC_NALU_END_OF_STREAM = 11,
    AVC_NALU_FILLER_DATA = 12,
    AVC_NALU_SPS_EXTENSION = 13,
    AVC_NALU_PREFIX = 14,
    AVC_NALU_SUBSET_SPS = 15,
    AVC_NALU_AUXILIARY_SLICE = 19,
    AVC_NALU_SLICE_EXTENSION = 20,
};

// AVC SPS (Sequence Parameter Set) structure
struct avc_sps_t
{
    uint8_t profile_idc;
    uint8_t constraint_set0_flag;
    uint8_t constraint_set1_flag;
    uint8_t constraint_set2_flag;
    uint8_t constraint_set3_flag;
    uint8_t constraint_set4_flag;
    uint8_t constraint_set5_flag;
    uint8_t level_idc;
    uint8_t seq_parameter_set_id;
    
    uint8_t chroma_format_idc;
    uint8_t separate_colour_plane_flag;
    uint8_t bit_depth_luma_minus8;
    uint8_t bit_depth_chroma_minus8;
    uint8_t qpprime_y_zero_transform_bypass_flag;
    uint8_t seq_scaling_matrix_present_flag;
    
    uint8_t log2_max_frame_num_minus4;
    uint8_t pic_order_cnt_type;
    uint8_t log2_max_pic_order_cnt_lsb_minus4;
    uint8_t delta_pic_order_always_zero_flag;
    int32_t offset_for_non_ref_pic;
    int32_t offset_for_top_to_bottom_field;
    uint8_t num_ref_frames_in_pic_order_cnt_cycle;
    
    uint8_t max_num_ref_frames;
    uint8_t gaps_in_frame_num_value_allowed_flag;
    uint32_t pic_width_in_mbs_minus1;
    uint32_t pic_height_in_map_units_minus1;
    uint8_t frame_mbs_only_flag;
    uint8_t mb_adaptive_frame_field_flag;
    uint8_t direct_8x8_inference_flag;
    uint8_t frame_cropping_flag;
    
    uint32_t frame_crop_left_offset;
    uint32_t frame_crop_right_offset;
    uint32_t frame_crop_top_offset;
    uint32_t frame_crop_bottom_offset;
    
    uint8_t vui_parameters_present_flag;
};

// AVC PPS (Picture Parameter Set) structure
struct avc_pps_t
{
    uint8_t pic_parameter_set_id;
    uint8_t seq_parameter_set_id;
    uint8_t entropy_coding_mode_flag;
    uint8_t bottom_field_pic_order_in_frame_present_flag;
    uint8_t num_slice_groups_minus1;
    uint8_t slice_group_map_type;
    uint8_t num_ref_idx_l0_default_active_minus1;
    uint8_t num_ref_idx_l1_default_active_minus1;
    uint8_t weighted_pred_flag;
    uint8_t weighted_bipred_idc;
    int8_t pic_init_qp_minus26;
    int8_t pic_init_qs_minus26;
    int8_t chroma_qp_index_offset;
    uint8_t deblocking_filter_control_present_flag;
    uint8_t constrained_intra_pred_flag;
    uint8_t redundant_pic_cnt_present_flag;
};

// AVC decoder configuration record
struct mpeg4_avc_t
{
    uint8_t configuration_version;
    uint8_t profile;
    uint8_t compatibility;
    uint8_t level;
    uint8_t nalu;

    // SPS
    uint8_t nb_sps;
    struct {
        uint16_t bytes;
        uint8_t *data;
    } sps[8];

    // PPS
    uint8_t nb_pps;
    struct {
        uint16_t bytes;
        uint8_t *data;
    } pps[8];

    // Data storage
    uint8_t data[1024];
};

// AVC parsing functions
int mpeg4_avc_decoder_configuration_record_load(const uint8_t* data, size_t bytes, struct mpeg4_avc_t* avc);
int mpeg4_avc_decoder_configuration_record_save(const struct mpeg4_avc_t* avc, uint8_t* data, size_t bytes);

// AVC parameter set parsing
int mpeg4_avc_sps_parse(const uint8_t* data, size_t bytes, struct avc_sps_t* sps);
int mpeg4_avc_pps_parse(const uint8_t* data, size_t bytes, struct avc_pps_t* pps);

// Utility functions
int mpeg4_avc_profile_level(const struct mpeg4_avc_t* avc);
enum avc_nalu_type_t mpeg4_avc_nalu_type(const uint8_t* nalu);

// Memory management
struct mpeg4_avc_t* mpeg4_avc_create(void);
void mpeg4_avc_destroy(struct mpeg4_avc_t* avc);

#ifdef __cplusplus
}
#endif

#endif /* !_mpeg4_avc_h_ */
