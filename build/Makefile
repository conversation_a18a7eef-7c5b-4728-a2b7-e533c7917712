# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/rtspSRV

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/rtspSRV/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/rtspSRV/build/CMakeFiles /home/<USER>/rtspSRV/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/rtspSRV/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named rtp

# Build rule for target.
rtp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rtp
.PHONY : rtp

# fast build rule for target.
rtp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/build
.PHONY : rtp/fast

#=============================================================================
# Target rules for targets named rtsp

# Build rule for target.
rtsp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rtsp
.PHONY : rtsp

# fast build rule for target.
rtsp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/build
.PHONY : rtsp/fast

#=============================================================================
# Target rules for targets named rtsp-server-test

# Build rule for target.
rtsp-server-test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rtsp-server-test
.PHONY : rtsp-server-test

# fast build rule for target.
rtsp-server-test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/build
.PHONY : rtsp-server-test/fast

aom-av1.o: aom-av1.c.o
.PHONY : aom-av1.o

# target to build an object file
aom-av1.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/aom-av1.c.o
.PHONY : aom-av1.c.o

aom-av1.i: aom-av1.c.i
.PHONY : aom-av1.i

# target to preprocess a source file
aom-av1.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/aom-av1.c.i
.PHONY : aom-av1.c.i

aom-av1.s: aom-av1.c.s
.PHONY : aom-av1.s

# target to generate assembly for a file
aom-av1.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/aom-av1.c.s
.PHONY : aom-av1.c.s

h264-file-reader.o: h264-file-reader.cpp.o
.PHONY : h264-file-reader.o

# target to build an object file
h264-file-reader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o
.PHONY : h264-file-reader.cpp.o

h264-file-reader.i: h264-file-reader.cpp.i
.PHONY : h264-file-reader.i

# target to preprocess a source file
h264-file-reader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.i
.PHONY : h264-file-reader.cpp.i

h264-file-reader.s: h264-file-reader.cpp.s
.PHONY : h264-file-reader.s

# target to generate assembly for a file
h264-file-reader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.s
.PHONY : h264-file-reader.cpp.s

h264-file-source.o: h264-file-source.cpp.o
.PHONY : h264-file-source.o

# target to build an object file
h264-file-source.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o
.PHONY : h264-file-source.cpp.o

h264-file-source.i: h264-file-source.cpp.i
.PHONY : h264-file-source.i

# target to preprocess a source file
h264-file-source.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.i
.PHONY : h264-file-source.cpp.i

h264-file-source.s: h264-file-source.cpp.s
.PHONY : h264-file-source.s

# target to generate assembly for a file
h264-file-source.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.s
.PHONY : h264-file-source.cpp.s

h265-file-reader.o: h265-file-reader.cpp.o
.PHONY : h265-file-reader.o

# target to build an object file
h265-file-reader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o
.PHONY : h265-file-reader.cpp.o

h265-file-reader.i: h265-file-reader.cpp.i
.PHONY : h265-file-reader.i

# target to preprocess a source file
h265-file-reader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.i
.PHONY : h265-file-reader.cpp.i

h265-file-reader.s: h265-file-reader.cpp.s
.PHONY : h265-file-reader.s

# target to generate assembly for a file
h265-file-reader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.s
.PHONY : h265-file-reader.cpp.s

h265-file-source.o: h265-file-source.cpp.o
.PHONY : h265-file-source.o

# target to build an object file
h265-file-source.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o
.PHONY : h265-file-source.cpp.o

h265-file-source.i: h265-file-source.cpp.i
.PHONY : h265-file-source.i

# target to preprocess a source file
h265-file-source.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.i
.PHONY : h265-file-source.cpp.i

h265-file-source.s: h265-file-source.cpp.s
.PHONY : h265-file-source.s

# target to generate assembly for a file
h265-file-source.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.s
.PHONY : h265-file-source.cpp.s

http-header-auth.o: http-header-auth.c.o
.PHONY : http-header-auth.o

# target to build an object file
http-header-auth.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-header-auth.c.o
.PHONY : http-header-auth.c.o

http-header-auth.i: http-header-auth.c.i
.PHONY : http-header-auth.i

# target to preprocess a source file
http-header-auth.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-header-auth.c.i
.PHONY : http-header-auth.c.i

http-header-auth.s: http-header-auth.c.s
.PHONY : http-header-auth.s

# target to generate assembly for a file
http-header-auth.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-header-auth.c.s
.PHONY : http-header-auth.c.s

http-parser.o: http-parser.c.o
.PHONY : http-parser.o

# target to build an object file
http-parser.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-parser.c.o
.PHONY : http-parser.c.o

http-parser.i: http-parser.c.i
.PHONY : http-parser.i

# target to preprocess a source file
http-parser.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-parser.c.i
.PHONY : http-parser.c.i

http-parser.s: http-parser.c.s
.PHONY : http-parser.s

# target to generate assembly for a file
http-parser.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-parser.c.s
.PHONY : http-parser.c.s

http-reason.o: http-reason.c.o
.PHONY : http-reason.o

# target to build an object file
http-reason.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-reason.c.o
.PHONY : http-reason.c.o

http-reason.i: http-reason.c.i
.PHONY : http-reason.i

# target to preprocess a source file
http-reason.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-reason.c.i
.PHONY : http-reason.c.i

http-reason.s: http-reason.c.s
.PHONY : http-reason.s

# target to generate assembly for a file
http-reason.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/http-reason.c.s
.PHONY : http-reason.c.s

librtp/payload/rtp-av1-pack.o: librtp/payload/rtp-av1-pack.c.o
.PHONY : librtp/payload/rtp-av1-pack.o

# target to build an object file
librtp/payload/rtp-av1-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o
.PHONY : librtp/payload/rtp-av1-pack.c.o

librtp/payload/rtp-av1-pack.i: librtp/payload/rtp-av1-pack.c.i
.PHONY : librtp/payload/rtp-av1-pack.i

# target to preprocess a source file
librtp/payload/rtp-av1-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.i
.PHONY : librtp/payload/rtp-av1-pack.c.i

librtp/payload/rtp-av1-pack.s: librtp/payload/rtp-av1-pack.c.s
.PHONY : librtp/payload/rtp-av1-pack.s

# target to generate assembly for a file
librtp/payload/rtp-av1-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.s
.PHONY : librtp/payload/rtp-av1-pack.c.s

librtp/payload/rtp-av1-unpack.o: librtp/payload/rtp-av1-unpack.c.o
.PHONY : librtp/payload/rtp-av1-unpack.o

# target to build an object file
librtp/payload/rtp-av1-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o
.PHONY : librtp/payload/rtp-av1-unpack.c.o

librtp/payload/rtp-av1-unpack.i: librtp/payload/rtp-av1-unpack.c.i
.PHONY : librtp/payload/rtp-av1-unpack.i

# target to preprocess a source file
librtp/payload/rtp-av1-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.i
.PHONY : librtp/payload/rtp-av1-unpack.c.i

librtp/payload/rtp-av1-unpack.s: librtp/payload/rtp-av1-unpack.c.s
.PHONY : librtp/payload/rtp-av1-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-av1-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.s
.PHONY : librtp/payload/rtp-av1-unpack.c.s

librtp/payload/rtp-h264-bitstream.o: librtp/payload/rtp-h264-bitstream.c.o
.PHONY : librtp/payload/rtp-h264-bitstream.o

# target to build an object file
librtp/payload/rtp-h264-bitstream.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o
.PHONY : librtp/payload/rtp-h264-bitstream.c.o

librtp/payload/rtp-h264-bitstream.i: librtp/payload/rtp-h264-bitstream.c.i
.PHONY : librtp/payload/rtp-h264-bitstream.i

# target to preprocess a source file
librtp/payload/rtp-h264-bitstream.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.i
.PHONY : librtp/payload/rtp-h264-bitstream.c.i

librtp/payload/rtp-h264-bitstream.s: librtp/payload/rtp-h264-bitstream.c.s
.PHONY : librtp/payload/rtp-h264-bitstream.s

# target to generate assembly for a file
librtp/payload/rtp-h264-bitstream.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.s
.PHONY : librtp/payload/rtp-h264-bitstream.c.s

librtp/payload/rtp-h264-pack.o: librtp/payload/rtp-h264-pack.c.o
.PHONY : librtp/payload/rtp-h264-pack.o

# target to build an object file
librtp/payload/rtp-h264-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o
.PHONY : librtp/payload/rtp-h264-pack.c.o

librtp/payload/rtp-h264-pack.i: librtp/payload/rtp-h264-pack.c.i
.PHONY : librtp/payload/rtp-h264-pack.i

# target to preprocess a source file
librtp/payload/rtp-h264-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.i
.PHONY : librtp/payload/rtp-h264-pack.c.i

librtp/payload/rtp-h264-pack.s: librtp/payload/rtp-h264-pack.c.s
.PHONY : librtp/payload/rtp-h264-pack.s

# target to generate assembly for a file
librtp/payload/rtp-h264-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.s
.PHONY : librtp/payload/rtp-h264-pack.c.s

librtp/payload/rtp-h264-unpack.o: librtp/payload/rtp-h264-unpack.c.o
.PHONY : librtp/payload/rtp-h264-unpack.o

# target to build an object file
librtp/payload/rtp-h264-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o
.PHONY : librtp/payload/rtp-h264-unpack.c.o

librtp/payload/rtp-h264-unpack.i: librtp/payload/rtp-h264-unpack.c.i
.PHONY : librtp/payload/rtp-h264-unpack.i

# target to preprocess a source file
librtp/payload/rtp-h264-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.i
.PHONY : librtp/payload/rtp-h264-unpack.c.i

librtp/payload/rtp-h264-unpack.s: librtp/payload/rtp-h264-unpack.c.s
.PHONY : librtp/payload/rtp-h264-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-h264-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.s
.PHONY : librtp/payload/rtp-h264-unpack.c.s

librtp/payload/rtp-h265-pack.o: librtp/payload/rtp-h265-pack.c.o
.PHONY : librtp/payload/rtp-h265-pack.o

# target to build an object file
librtp/payload/rtp-h265-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o
.PHONY : librtp/payload/rtp-h265-pack.c.o

librtp/payload/rtp-h265-pack.i: librtp/payload/rtp-h265-pack.c.i
.PHONY : librtp/payload/rtp-h265-pack.i

# target to preprocess a source file
librtp/payload/rtp-h265-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.i
.PHONY : librtp/payload/rtp-h265-pack.c.i

librtp/payload/rtp-h265-pack.s: librtp/payload/rtp-h265-pack.c.s
.PHONY : librtp/payload/rtp-h265-pack.s

# target to generate assembly for a file
librtp/payload/rtp-h265-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.s
.PHONY : librtp/payload/rtp-h265-pack.c.s

librtp/payload/rtp-h265-unpack.o: librtp/payload/rtp-h265-unpack.c.o
.PHONY : librtp/payload/rtp-h265-unpack.o

# target to build an object file
librtp/payload/rtp-h265-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o
.PHONY : librtp/payload/rtp-h265-unpack.c.o

librtp/payload/rtp-h265-unpack.i: librtp/payload/rtp-h265-unpack.c.i
.PHONY : librtp/payload/rtp-h265-unpack.i

# target to preprocess a source file
librtp/payload/rtp-h265-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.i
.PHONY : librtp/payload/rtp-h265-unpack.c.i

librtp/payload/rtp-h265-unpack.s: librtp/payload/rtp-h265-unpack.c.s
.PHONY : librtp/payload/rtp-h265-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-h265-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.s
.PHONY : librtp/payload/rtp-h265-unpack.c.s

librtp/payload/rtp-h266-pack.o: librtp/payload/rtp-h266-pack.c.o
.PHONY : librtp/payload/rtp-h266-pack.o

# target to build an object file
librtp/payload/rtp-h266-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o
.PHONY : librtp/payload/rtp-h266-pack.c.o

librtp/payload/rtp-h266-pack.i: librtp/payload/rtp-h266-pack.c.i
.PHONY : librtp/payload/rtp-h266-pack.i

# target to preprocess a source file
librtp/payload/rtp-h266-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.i
.PHONY : librtp/payload/rtp-h266-pack.c.i

librtp/payload/rtp-h266-pack.s: librtp/payload/rtp-h266-pack.c.s
.PHONY : librtp/payload/rtp-h266-pack.s

# target to generate assembly for a file
librtp/payload/rtp-h266-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.s
.PHONY : librtp/payload/rtp-h266-pack.c.s

librtp/payload/rtp-h266-unpack.o: librtp/payload/rtp-h266-unpack.c.o
.PHONY : librtp/payload/rtp-h266-unpack.o

# target to build an object file
librtp/payload/rtp-h266-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o
.PHONY : librtp/payload/rtp-h266-unpack.c.o

librtp/payload/rtp-h266-unpack.i: librtp/payload/rtp-h266-unpack.c.i
.PHONY : librtp/payload/rtp-h266-unpack.i

# target to preprocess a source file
librtp/payload/rtp-h266-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.i
.PHONY : librtp/payload/rtp-h266-unpack.c.i

librtp/payload/rtp-h266-unpack.s: librtp/payload/rtp-h266-unpack.c.s
.PHONY : librtp/payload/rtp-h266-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-h266-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.s
.PHONY : librtp/payload/rtp-h266-unpack.c.s

librtp/payload/rtp-mp4a-latm-pack.o: librtp/payload/rtp-mp4a-latm-pack.c.o
.PHONY : librtp/payload/rtp-mp4a-latm-pack.o

# target to build an object file
librtp/payload/rtp-mp4a-latm-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o
.PHONY : librtp/payload/rtp-mp4a-latm-pack.c.o

librtp/payload/rtp-mp4a-latm-pack.i: librtp/payload/rtp-mp4a-latm-pack.c.i
.PHONY : librtp/payload/rtp-mp4a-latm-pack.i

# target to preprocess a source file
librtp/payload/rtp-mp4a-latm-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.i
.PHONY : librtp/payload/rtp-mp4a-latm-pack.c.i

librtp/payload/rtp-mp4a-latm-pack.s: librtp/payload/rtp-mp4a-latm-pack.c.s
.PHONY : librtp/payload/rtp-mp4a-latm-pack.s

# target to generate assembly for a file
librtp/payload/rtp-mp4a-latm-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.s
.PHONY : librtp/payload/rtp-mp4a-latm-pack.c.s

librtp/payload/rtp-mp4a-latm-unpack.o: librtp/payload/rtp-mp4a-latm-unpack.c.o
.PHONY : librtp/payload/rtp-mp4a-latm-unpack.o

# target to build an object file
librtp/payload/rtp-mp4a-latm-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o
.PHONY : librtp/payload/rtp-mp4a-latm-unpack.c.o

librtp/payload/rtp-mp4a-latm-unpack.i: librtp/payload/rtp-mp4a-latm-unpack.c.i
.PHONY : librtp/payload/rtp-mp4a-latm-unpack.i

# target to preprocess a source file
librtp/payload/rtp-mp4a-latm-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.i
.PHONY : librtp/payload/rtp-mp4a-latm-unpack.c.i

librtp/payload/rtp-mp4a-latm-unpack.s: librtp/payload/rtp-mp4a-latm-unpack.c.s
.PHONY : librtp/payload/rtp-mp4a-latm-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-mp4a-latm-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.s
.PHONY : librtp/payload/rtp-mp4a-latm-unpack.c.s

librtp/payload/rtp-mp4v-es-pack.o: librtp/payload/rtp-mp4v-es-pack.c.o
.PHONY : librtp/payload/rtp-mp4v-es-pack.o

# target to build an object file
librtp/payload/rtp-mp4v-es-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o
.PHONY : librtp/payload/rtp-mp4v-es-pack.c.o

librtp/payload/rtp-mp4v-es-pack.i: librtp/payload/rtp-mp4v-es-pack.c.i
.PHONY : librtp/payload/rtp-mp4v-es-pack.i

# target to preprocess a source file
librtp/payload/rtp-mp4v-es-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.i
.PHONY : librtp/payload/rtp-mp4v-es-pack.c.i

librtp/payload/rtp-mp4v-es-pack.s: librtp/payload/rtp-mp4v-es-pack.c.s
.PHONY : librtp/payload/rtp-mp4v-es-pack.s

# target to generate assembly for a file
librtp/payload/rtp-mp4v-es-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.s
.PHONY : librtp/payload/rtp-mp4v-es-pack.c.s

librtp/payload/rtp-mp4v-es-unpack.o: librtp/payload/rtp-mp4v-es-unpack.c.o
.PHONY : librtp/payload/rtp-mp4v-es-unpack.o

# target to build an object file
librtp/payload/rtp-mp4v-es-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o
.PHONY : librtp/payload/rtp-mp4v-es-unpack.c.o

librtp/payload/rtp-mp4v-es-unpack.i: librtp/payload/rtp-mp4v-es-unpack.c.i
.PHONY : librtp/payload/rtp-mp4v-es-unpack.i

# target to preprocess a source file
librtp/payload/rtp-mp4v-es-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.i
.PHONY : librtp/payload/rtp-mp4v-es-unpack.c.i

librtp/payload/rtp-mp4v-es-unpack.s: librtp/payload/rtp-mp4v-es-unpack.c.s
.PHONY : librtp/payload/rtp-mp4v-es-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-mp4v-es-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.s
.PHONY : librtp/payload/rtp-mp4v-es-unpack.c.s

librtp/payload/rtp-mpeg1or2es-pack.o: librtp/payload/rtp-mpeg1or2es-pack.c.o
.PHONY : librtp/payload/rtp-mpeg1or2es-pack.o

# target to build an object file
librtp/payload/rtp-mpeg1or2es-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o
.PHONY : librtp/payload/rtp-mpeg1or2es-pack.c.o

librtp/payload/rtp-mpeg1or2es-pack.i: librtp/payload/rtp-mpeg1or2es-pack.c.i
.PHONY : librtp/payload/rtp-mpeg1or2es-pack.i

# target to preprocess a source file
librtp/payload/rtp-mpeg1or2es-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.i
.PHONY : librtp/payload/rtp-mpeg1or2es-pack.c.i

librtp/payload/rtp-mpeg1or2es-pack.s: librtp/payload/rtp-mpeg1or2es-pack.c.s
.PHONY : librtp/payload/rtp-mpeg1or2es-pack.s

# target to generate assembly for a file
librtp/payload/rtp-mpeg1or2es-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.s
.PHONY : librtp/payload/rtp-mpeg1or2es-pack.c.s

librtp/payload/rtp-mpeg1or2es-unpack.o: librtp/payload/rtp-mpeg1or2es-unpack.c.o
.PHONY : librtp/payload/rtp-mpeg1or2es-unpack.o

# target to build an object file
librtp/payload/rtp-mpeg1or2es-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o
.PHONY : librtp/payload/rtp-mpeg1or2es-unpack.c.o

librtp/payload/rtp-mpeg1or2es-unpack.i: librtp/payload/rtp-mpeg1or2es-unpack.c.i
.PHONY : librtp/payload/rtp-mpeg1or2es-unpack.i

# target to preprocess a source file
librtp/payload/rtp-mpeg1or2es-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.i
.PHONY : librtp/payload/rtp-mpeg1or2es-unpack.c.i

librtp/payload/rtp-mpeg1or2es-unpack.s: librtp/payload/rtp-mpeg1or2es-unpack.c.s
.PHONY : librtp/payload/rtp-mpeg1or2es-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-mpeg1or2es-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.s
.PHONY : librtp/payload/rtp-mpeg1or2es-unpack.c.s

librtp/payload/rtp-mpeg4-generic-pack.o: librtp/payload/rtp-mpeg4-generic-pack.c.o
.PHONY : librtp/payload/rtp-mpeg4-generic-pack.o

# target to build an object file
librtp/payload/rtp-mpeg4-generic-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o
.PHONY : librtp/payload/rtp-mpeg4-generic-pack.c.o

librtp/payload/rtp-mpeg4-generic-pack.i: librtp/payload/rtp-mpeg4-generic-pack.c.i
.PHONY : librtp/payload/rtp-mpeg4-generic-pack.i

# target to preprocess a source file
librtp/payload/rtp-mpeg4-generic-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.i
.PHONY : librtp/payload/rtp-mpeg4-generic-pack.c.i

librtp/payload/rtp-mpeg4-generic-pack.s: librtp/payload/rtp-mpeg4-generic-pack.c.s
.PHONY : librtp/payload/rtp-mpeg4-generic-pack.s

# target to generate assembly for a file
librtp/payload/rtp-mpeg4-generic-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.s
.PHONY : librtp/payload/rtp-mpeg4-generic-pack.c.s

librtp/payload/rtp-mpeg4-generic-unpack.o: librtp/payload/rtp-mpeg4-generic-unpack.c.o
.PHONY : librtp/payload/rtp-mpeg4-generic-unpack.o

# target to build an object file
librtp/payload/rtp-mpeg4-generic-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o
.PHONY : librtp/payload/rtp-mpeg4-generic-unpack.c.o

librtp/payload/rtp-mpeg4-generic-unpack.i: librtp/payload/rtp-mpeg4-generic-unpack.c.i
.PHONY : librtp/payload/rtp-mpeg4-generic-unpack.i

# target to preprocess a source file
librtp/payload/rtp-mpeg4-generic-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.i
.PHONY : librtp/payload/rtp-mpeg4-generic-unpack.c.i

librtp/payload/rtp-mpeg4-generic-unpack.s: librtp/payload/rtp-mpeg4-generic-unpack.c.s
.PHONY : librtp/payload/rtp-mpeg4-generic-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-mpeg4-generic-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.s
.PHONY : librtp/payload/rtp-mpeg4-generic-unpack.c.s

librtp/payload/rtp-pack.o: librtp/payload/rtp-pack.c.o
.PHONY : librtp/payload/rtp-pack.o

# target to build an object file
librtp/payload/rtp-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o
.PHONY : librtp/payload/rtp-pack.c.o

librtp/payload/rtp-pack.i: librtp/payload/rtp-pack.c.i
.PHONY : librtp/payload/rtp-pack.i

# target to preprocess a source file
librtp/payload/rtp-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.i
.PHONY : librtp/payload/rtp-pack.c.i

librtp/payload/rtp-pack.s: librtp/payload/rtp-pack.c.s
.PHONY : librtp/payload/rtp-pack.s

# target to generate assembly for a file
librtp/payload/rtp-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.s
.PHONY : librtp/payload/rtp-pack.c.s

librtp/payload/rtp-payload-helper.o: librtp/payload/rtp-payload-helper.c.o
.PHONY : librtp/payload/rtp-payload-helper.o

# target to build an object file
librtp/payload/rtp-payload-helper.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o
.PHONY : librtp/payload/rtp-payload-helper.c.o

librtp/payload/rtp-payload-helper.i: librtp/payload/rtp-payload-helper.c.i
.PHONY : librtp/payload/rtp-payload-helper.i

# target to preprocess a source file
librtp/payload/rtp-payload-helper.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.i
.PHONY : librtp/payload/rtp-payload-helper.c.i

librtp/payload/rtp-payload-helper.s: librtp/payload/rtp-payload-helper.c.s
.PHONY : librtp/payload/rtp-payload-helper.s

# target to generate assembly for a file
librtp/payload/rtp-payload-helper.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.s
.PHONY : librtp/payload/rtp-payload-helper.c.s

librtp/payload/rtp-payload.o: librtp/payload/rtp-payload.c.o
.PHONY : librtp/payload/rtp-payload.o

# target to build an object file
librtp/payload/rtp-payload.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o
.PHONY : librtp/payload/rtp-payload.c.o

librtp/payload/rtp-payload.i: librtp/payload/rtp-payload.c.i
.PHONY : librtp/payload/rtp-payload.i

# target to preprocess a source file
librtp/payload/rtp-payload.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.i
.PHONY : librtp/payload/rtp-payload.c.i

librtp/payload/rtp-payload.s: librtp/payload/rtp-payload.c.s
.PHONY : librtp/payload/rtp-payload.s

# target to generate assembly for a file
librtp/payload/rtp-payload.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.s
.PHONY : librtp/payload/rtp-payload.c.s

librtp/payload/rtp-ps-unpack.o: librtp/payload/rtp-ps-unpack.c.o
.PHONY : librtp/payload/rtp-ps-unpack.o

# target to build an object file
librtp/payload/rtp-ps-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o
.PHONY : librtp/payload/rtp-ps-unpack.c.o

librtp/payload/rtp-ps-unpack.i: librtp/payload/rtp-ps-unpack.c.i
.PHONY : librtp/payload/rtp-ps-unpack.i

# target to preprocess a source file
librtp/payload/rtp-ps-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.i
.PHONY : librtp/payload/rtp-ps-unpack.c.i

librtp/payload/rtp-ps-unpack.s: librtp/payload/rtp-ps-unpack.c.s
.PHONY : librtp/payload/rtp-ps-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-ps-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.s
.PHONY : librtp/payload/rtp-ps-unpack.c.s

librtp/payload/rtp-ts-pack.o: librtp/payload/rtp-ts-pack.c.o
.PHONY : librtp/payload/rtp-ts-pack.o

# target to build an object file
librtp/payload/rtp-ts-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o
.PHONY : librtp/payload/rtp-ts-pack.c.o

librtp/payload/rtp-ts-pack.i: librtp/payload/rtp-ts-pack.c.i
.PHONY : librtp/payload/rtp-ts-pack.i

# target to preprocess a source file
librtp/payload/rtp-ts-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.i
.PHONY : librtp/payload/rtp-ts-pack.c.i

librtp/payload/rtp-ts-pack.s: librtp/payload/rtp-ts-pack.c.s
.PHONY : librtp/payload/rtp-ts-pack.s

# target to generate assembly for a file
librtp/payload/rtp-ts-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.s
.PHONY : librtp/payload/rtp-ts-pack.c.s

librtp/payload/rtp-ts-unpack.o: librtp/payload/rtp-ts-unpack.c.o
.PHONY : librtp/payload/rtp-ts-unpack.o

# target to build an object file
librtp/payload/rtp-ts-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o
.PHONY : librtp/payload/rtp-ts-unpack.c.o

librtp/payload/rtp-ts-unpack.i: librtp/payload/rtp-ts-unpack.c.i
.PHONY : librtp/payload/rtp-ts-unpack.i

# target to preprocess a source file
librtp/payload/rtp-ts-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.i
.PHONY : librtp/payload/rtp-ts-unpack.c.i

librtp/payload/rtp-ts-unpack.s: librtp/payload/rtp-ts-unpack.c.s
.PHONY : librtp/payload/rtp-ts-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-ts-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.s
.PHONY : librtp/payload/rtp-ts-unpack.c.s

librtp/payload/rtp-unpack.o: librtp/payload/rtp-unpack.c.o
.PHONY : librtp/payload/rtp-unpack.o

# target to build an object file
librtp/payload/rtp-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o
.PHONY : librtp/payload/rtp-unpack.c.o

librtp/payload/rtp-unpack.i: librtp/payload/rtp-unpack.c.i
.PHONY : librtp/payload/rtp-unpack.i

# target to preprocess a source file
librtp/payload/rtp-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.i
.PHONY : librtp/payload/rtp-unpack.c.i

librtp/payload/rtp-unpack.s: librtp/payload/rtp-unpack.c.s
.PHONY : librtp/payload/rtp-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.s
.PHONY : librtp/payload/rtp-unpack.c.s

librtp/payload/rtp-vp8-pack.o: librtp/payload/rtp-vp8-pack.c.o
.PHONY : librtp/payload/rtp-vp8-pack.o

# target to build an object file
librtp/payload/rtp-vp8-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o
.PHONY : librtp/payload/rtp-vp8-pack.c.o

librtp/payload/rtp-vp8-pack.i: librtp/payload/rtp-vp8-pack.c.i
.PHONY : librtp/payload/rtp-vp8-pack.i

# target to preprocess a source file
librtp/payload/rtp-vp8-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.i
.PHONY : librtp/payload/rtp-vp8-pack.c.i

librtp/payload/rtp-vp8-pack.s: librtp/payload/rtp-vp8-pack.c.s
.PHONY : librtp/payload/rtp-vp8-pack.s

# target to generate assembly for a file
librtp/payload/rtp-vp8-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.s
.PHONY : librtp/payload/rtp-vp8-pack.c.s

librtp/payload/rtp-vp8-unpack.o: librtp/payload/rtp-vp8-unpack.c.o
.PHONY : librtp/payload/rtp-vp8-unpack.o

# target to build an object file
librtp/payload/rtp-vp8-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o
.PHONY : librtp/payload/rtp-vp8-unpack.c.o

librtp/payload/rtp-vp8-unpack.i: librtp/payload/rtp-vp8-unpack.c.i
.PHONY : librtp/payload/rtp-vp8-unpack.i

# target to preprocess a source file
librtp/payload/rtp-vp8-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.i
.PHONY : librtp/payload/rtp-vp8-unpack.c.i

librtp/payload/rtp-vp8-unpack.s: librtp/payload/rtp-vp8-unpack.c.s
.PHONY : librtp/payload/rtp-vp8-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-vp8-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.s
.PHONY : librtp/payload/rtp-vp8-unpack.c.s

librtp/payload/rtp-vp9-pack.o: librtp/payload/rtp-vp9-pack.c.o
.PHONY : librtp/payload/rtp-vp9-pack.o

# target to build an object file
librtp/payload/rtp-vp9-pack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o
.PHONY : librtp/payload/rtp-vp9-pack.c.o

librtp/payload/rtp-vp9-pack.i: librtp/payload/rtp-vp9-pack.c.i
.PHONY : librtp/payload/rtp-vp9-pack.i

# target to preprocess a source file
librtp/payload/rtp-vp9-pack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.i
.PHONY : librtp/payload/rtp-vp9-pack.c.i

librtp/payload/rtp-vp9-pack.s: librtp/payload/rtp-vp9-pack.c.s
.PHONY : librtp/payload/rtp-vp9-pack.s

# target to generate assembly for a file
librtp/payload/rtp-vp9-pack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.s
.PHONY : librtp/payload/rtp-vp9-pack.c.s

librtp/payload/rtp-vp9-unpack.o: librtp/payload/rtp-vp9-unpack.c.o
.PHONY : librtp/payload/rtp-vp9-unpack.o

# target to build an object file
librtp/payload/rtp-vp9-unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o
.PHONY : librtp/payload/rtp-vp9-unpack.c.o

librtp/payload/rtp-vp9-unpack.i: librtp/payload/rtp-vp9-unpack.c.i
.PHONY : librtp/payload/rtp-vp9-unpack.i

# target to preprocess a source file
librtp/payload/rtp-vp9-unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.i
.PHONY : librtp/payload/rtp-vp9-unpack.c.i

librtp/payload/rtp-vp9-unpack.s: librtp/payload/rtp-vp9-unpack.c.s
.PHONY : librtp/payload/rtp-vp9-unpack.s

# target to generate assembly for a file
librtp/payload/rtp-vp9-unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.s
.PHONY : librtp/payload/rtp-vp9-unpack.c.s

librtp/source/rtcp-app.o: librtp/source/rtcp-app.c.o
.PHONY : librtp/source/rtcp-app.o

# target to build an object file
librtp/source/rtcp-app.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o
.PHONY : librtp/source/rtcp-app.c.o

librtp/source/rtcp-app.i: librtp/source/rtcp-app.c.i
.PHONY : librtp/source/rtcp-app.i

# target to preprocess a source file
librtp/source/rtcp-app.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.i
.PHONY : librtp/source/rtcp-app.c.i

librtp/source/rtcp-app.s: librtp/source/rtcp-app.c.s
.PHONY : librtp/source/rtcp-app.s

# target to generate assembly for a file
librtp/source/rtcp-app.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.s
.PHONY : librtp/source/rtcp-app.c.s

librtp/source/rtcp-bye.o: librtp/source/rtcp-bye.c.o
.PHONY : librtp/source/rtcp-bye.o

# target to build an object file
librtp/source/rtcp-bye.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o
.PHONY : librtp/source/rtcp-bye.c.o

librtp/source/rtcp-bye.i: librtp/source/rtcp-bye.c.i
.PHONY : librtp/source/rtcp-bye.i

# target to preprocess a source file
librtp/source/rtcp-bye.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.i
.PHONY : librtp/source/rtcp-bye.c.i

librtp/source/rtcp-bye.s: librtp/source/rtcp-bye.c.s
.PHONY : librtp/source/rtcp-bye.s

# target to generate assembly for a file
librtp/source/rtcp-bye.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.s
.PHONY : librtp/source/rtcp-bye.c.s

librtp/source/rtcp-interval.o: librtp/source/rtcp-interval.c.o
.PHONY : librtp/source/rtcp-interval.o

# target to build an object file
librtp/source/rtcp-interval.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o
.PHONY : librtp/source/rtcp-interval.c.o

librtp/source/rtcp-interval.i: librtp/source/rtcp-interval.c.i
.PHONY : librtp/source/rtcp-interval.i

# target to preprocess a source file
librtp/source/rtcp-interval.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.i
.PHONY : librtp/source/rtcp-interval.c.i

librtp/source/rtcp-interval.s: librtp/source/rtcp-interval.c.s
.PHONY : librtp/source/rtcp-interval.s

# target to generate assembly for a file
librtp/source/rtcp-interval.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.s
.PHONY : librtp/source/rtcp-interval.c.s

librtp/source/rtcp-psfb.o: librtp/source/rtcp-psfb.c.o
.PHONY : librtp/source/rtcp-psfb.o

# target to build an object file
librtp/source/rtcp-psfb.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o
.PHONY : librtp/source/rtcp-psfb.c.o

librtp/source/rtcp-psfb.i: librtp/source/rtcp-psfb.c.i
.PHONY : librtp/source/rtcp-psfb.i

# target to preprocess a source file
librtp/source/rtcp-psfb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.i
.PHONY : librtp/source/rtcp-psfb.c.i

librtp/source/rtcp-psfb.s: librtp/source/rtcp-psfb.c.s
.PHONY : librtp/source/rtcp-psfb.s

# target to generate assembly for a file
librtp/source/rtcp-psfb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.s
.PHONY : librtp/source/rtcp-psfb.c.s

librtp/source/rtcp-rr.o: librtp/source/rtcp-rr.c.o
.PHONY : librtp/source/rtcp-rr.o

# target to build an object file
librtp/source/rtcp-rr.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o
.PHONY : librtp/source/rtcp-rr.c.o

librtp/source/rtcp-rr.i: librtp/source/rtcp-rr.c.i
.PHONY : librtp/source/rtcp-rr.i

# target to preprocess a source file
librtp/source/rtcp-rr.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.i
.PHONY : librtp/source/rtcp-rr.c.i

librtp/source/rtcp-rr.s: librtp/source/rtcp-rr.c.s
.PHONY : librtp/source/rtcp-rr.s

# target to generate assembly for a file
librtp/source/rtcp-rr.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.s
.PHONY : librtp/source/rtcp-rr.c.s

librtp/source/rtcp-rtpfb.o: librtp/source/rtcp-rtpfb.c.o
.PHONY : librtp/source/rtcp-rtpfb.o

# target to build an object file
librtp/source/rtcp-rtpfb.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o
.PHONY : librtp/source/rtcp-rtpfb.c.o

librtp/source/rtcp-rtpfb.i: librtp/source/rtcp-rtpfb.c.i
.PHONY : librtp/source/rtcp-rtpfb.i

# target to preprocess a source file
librtp/source/rtcp-rtpfb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.i
.PHONY : librtp/source/rtcp-rtpfb.c.i

librtp/source/rtcp-rtpfb.s: librtp/source/rtcp-rtpfb.c.s
.PHONY : librtp/source/rtcp-rtpfb.s

# target to generate assembly for a file
librtp/source/rtcp-rtpfb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.s
.PHONY : librtp/source/rtcp-rtpfb.c.s

librtp/source/rtcp-sdec.o: librtp/source/rtcp-sdec.c.o
.PHONY : librtp/source/rtcp-sdec.o

# target to build an object file
librtp/source/rtcp-sdec.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o
.PHONY : librtp/source/rtcp-sdec.c.o

librtp/source/rtcp-sdec.i: librtp/source/rtcp-sdec.c.i
.PHONY : librtp/source/rtcp-sdec.i

# target to preprocess a source file
librtp/source/rtcp-sdec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.i
.PHONY : librtp/source/rtcp-sdec.c.i

librtp/source/rtcp-sdec.s: librtp/source/rtcp-sdec.c.s
.PHONY : librtp/source/rtcp-sdec.s

# target to generate assembly for a file
librtp/source/rtcp-sdec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.s
.PHONY : librtp/source/rtcp-sdec.c.s

librtp/source/rtcp-sr.o: librtp/source/rtcp-sr.c.o
.PHONY : librtp/source/rtcp-sr.o

# target to build an object file
librtp/source/rtcp-sr.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o
.PHONY : librtp/source/rtcp-sr.c.o

librtp/source/rtcp-sr.i: librtp/source/rtcp-sr.c.i
.PHONY : librtp/source/rtcp-sr.i

# target to preprocess a source file
librtp/source/rtcp-sr.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.i
.PHONY : librtp/source/rtcp-sr.c.i

librtp/source/rtcp-sr.s: librtp/source/rtcp-sr.c.s
.PHONY : librtp/source/rtcp-sr.s

# target to generate assembly for a file
librtp/source/rtcp-sr.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.s
.PHONY : librtp/source/rtcp-sr.c.s

librtp/source/rtcp-xr.o: librtp/source/rtcp-xr.c.o
.PHONY : librtp/source/rtcp-xr.o

# target to build an object file
librtp/source/rtcp-xr.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o
.PHONY : librtp/source/rtcp-xr.c.o

librtp/source/rtcp-xr.i: librtp/source/rtcp-xr.c.i
.PHONY : librtp/source/rtcp-xr.i

# target to preprocess a source file
librtp/source/rtcp-xr.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.i
.PHONY : librtp/source/rtcp-xr.c.i

librtp/source/rtcp-xr.s: librtp/source/rtcp-xr.c.s
.PHONY : librtp/source/rtcp-xr.s

# target to generate assembly for a file
librtp/source/rtcp-xr.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.s
.PHONY : librtp/source/rtcp-xr.c.s

librtp/source/rtcp.o: librtp/source/rtcp.c.o
.PHONY : librtp/source/rtcp.o

# target to build an object file
librtp/source/rtcp.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp.c.o
.PHONY : librtp/source/rtcp.c.o

librtp/source/rtcp.i: librtp/source/rtcp.c.i
.PHONY : librtp/source/rtcp.i

# target to preprocess a source file
librtp/source/rtcp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp.c.i
.PHONY : librtp/source/rtcp.c.i

librtp/source/rtcp.s: librtp/source/rtcp.c.s
.PHONY : librtp/source/rtcp.s

# target to generate assembly for a file
librtp/source/rtcp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtcp.c.s
.PHONY : librtp/source/rtcp.c.s

librtp/source/rtp-demuxer.o: librtp/source/rtp-demuxer.c.o
.PHONY : librtp/source/rtp-demuxer.o

# target to build an object file
librtp/source/rtp-demuxer.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o
.PHONY : librtp/source/rtp-demuxer.c.o

librtp/source/rtp-demuxer.i: librtp/source/rtp-demuxer.c.i
.PHONY : librtp/source/rtp-demuxer.i

# target to preprocess a source file
librtp/source/rtp-demuxer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.i
.PHONY : librtp/source/rtp-demuxer.c.i

librtp/source/rtp-demuxer.s: librtp/source/rtp-demuxer.c.s
.PHONY : librtp/source/rtp-demuxer.s

# target to generate assembly for a file
librtp/source/rtp-demuxer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.s
.PHONY : librtp/source/rtp-demuxer.c.s

librtp/source/rtp-member-list.o: librtp/source/rtp-member-list.c.o
.PHONY : librtp/source/rtp-member-list.o

# target to build an object file
librtp/source/rtp-member-list.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o
.PHONY : librtp/source/rtp-member-list.c.o

librtp/source/rtp-member-list.i: librtp/source/rtp-member-list.c.i
.PHONY : librtp/source/rtp-member-list.i

# target to preprocess a source file
librtp/source/rtp-member-list.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.i
.PHONY : librtp/source/rtp-member-list.c.i

librtp/source/rtp-member-list.s: librtp/source/rtp-member-list.c.s
.PHONY : librtp/source/rtp-member-list.s

# target to generate assembly for a file
librtp/source/rtp-member-list.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.s
.PHONY : librtp/source/rtp-member-list.c.s

librtp/source/rtp-member.o: librtp/source/rtp-member.c.o
.PHONY : librtp/source/rtp-member.o

# target to build an object file
librtp/source/rtp-member.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o
.PHONY : librtp/source/rtp-member.c.o

librtp/source/rtp-member.i: librtp/source/rtp-member.c.i
.PHONY : librtp/source/rtp-member.i

# target to preprocess a source file
librtp/source/rtp-member.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-member.c.i
.PHONY : librtp/source/rtp-member.c.i

librtp/source/rtp-member.s: librtp/source/rtp-member.c.s
.PHONY : librtp/source/rtp-member.s

# target to generate assembly for a file
librtp/source/rtp-member.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-member.c.s
.PHONY : librtp/source/rtp-member.c.s

librtp/source/rtp-packet.o: librtp/source/rtp-packet.c.o
.PHONY : librtp/source/rtp-packet.o

# target to build an object file
librtp/source/rtp-packet.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o
.PHONY : librtp/source/rtp-packet.c.o

librtp/source/rtp-packet.i: librtp/source/rtp-packet.c.i
.PHONY : librtp/source/rtp-packet.i

# target to preprocess a source file
librtp/source/rtp-packet.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.i
.PHONY : librtp/source/rtp-packet.c.i

librtp/source/rtp-packet.s: librtp/source/rtp-packet.c.s
.PHONY : librtp/source/rtp-packet.s

# target to generate assembly for a file
librtp/source/rtp-packet.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.s
.PHONY : librtp/source/rtp-packet.c.s

librtp/source/rtp-profile.o: librtp/source/rtp-profile.c.o
.PHONY : librtp/source/rtp-profile.o

# target to build an object file
librtp/source/rtp-profile.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o
.PHONY : librtp/source/rtp-profile.c.o

librtp/source/rtp-profile.i: librtp/source/rtp-profile.c.i
.PHONY : librtp/source/rtp-profile.i

# target to preprocess a source file
librtp/source/rtp-profile.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.i
.PHONY : librtp/source/rtp-profile.c.i

librtp/source/rtp-profile.s: librtp/source/rtp-profile.c.s
.PHONY : librtp/source/rtp-profile.s

# target to generate assembly for a file
librtp/source/rtp-profile.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.s
.PHONY : librtp/source/rtp-profile.c.s

librtp/source/rtp-queue.o: librtp/source/rtp-queue.c.o
.PHONY : librtp/source/rtp-queue.o

# target to build an object file
librtp/source/rtp-queue.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o
.PHONY : librtp/source/rtp-queue.c.o

librtp/source/rtp-queue.i: librtp/source/rtp-queue.c.i
.PHONY : librtp/source/rtp-queue.i

# target to preprocess a source file
librtp/source/rtp-queue.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.i
.PHONY : librtp/source/rtp-queue.c.i

librtp/source/rtp-queue.s: librtp/source/rtp-queue.c.s
.PHONY : librtp/source/rtp-queue.s

# target to generate assembly for a file
librtp/source/rtp-queue.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.s
.PHONY : librtp/source/rtp-queue.c.s

librtp/source/rtp-ssrc.o: librtp/source/rtp-ssrc.c.o
.PHONY : librtp/source/rtp-ssrc.o

# target to build an object file
librtp/source/rtp-ssrc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o
.PHONY : librtp/source/rtp-ssrc.c.o

librtp/source/rtp-ssrc.i: librtp/source/rtp-ssrc.c.i
.PHONY : librtp/source/rtp-ssrc.i

# target to preprocess a source file
librtp/source/rtp-ssrc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.i
.PHONY : librtp/source/rtp-ssrc.c.i

librtp/source/rtp-ssrc.s: librtp/source/rtp-ssrc.c.s
.PHONY : librtp/source/rtp-ssrc.s

# target to generate assembly for a file
librtp/source/rtp-ssrc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.s
.PHONY : librtp/source/rtp-ssrc.c.s

librtp/source/rtp-time.o: librtp/source/rtp-time.c.o
.PHONY : librtp/source/rtp-time.o

# target to build an object file
librtp/source/rtp-time.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o
.PHONY : librtp/source/rtp-time.c.o

librtp/source/rtp-time.i: librtp/source/rtp-time.c.i
.PHONY : librtp/source/rtp-time.i

# target to preprocess a source file
librtp/source/rtp-time.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-time.c.i
.PHONY : librtp/source/rtp-time.c.i

librtp/source/rtp-time.s: librtp/source/rtp-time.c.s
.PHONY : librtp/source/rtp-time.s

# target to generate assembly for a file
librtp/source/rtp-time.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp-time.c.s
.PHONY : librtp/source/rtp-time.c.s

librtp/source/rtp.o: librtp/source/rtp.c.o
.PHONY : librtp/source/rtp.o

# target to build an object file
librtp/source/rtp.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp.c.o
.PHONY : librtp/source/rtp.c.o

librtp/source/rtp.i: librtp/source/rtp.c.i
.PHONY : librtp/source/rtp.i

# target to preprocess a source file
librtp/source/rtp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp.c.i
.PHONY : librtp/source/rtp.c.i

librtp/source/rtp.s: librtp/source/rtp.c.s
.PHONY : librtp/source/rtp.s

# target to generate assembly for a file
librtp/source/rtp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtp.dir/build.make CMakeFiles/rtp.dir/librtp/source/rtp.c.s
.PHONY : librtp/source/rtp.c.s

librtsp/sdp/sdp-aac.o: librtsp/sdp/sdp-aac.c.o
.PHONY : librtsp/sdp/sdp-aac.o

# target to build an object file
librtsp/sdp/sdp-aac.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-aac.c.o
.PHONY : librtsp/sdp/sdp-aac.c.o

librtsp/sdp/sdp-aac.i: librtsp/sdp/sdp-aac.c.i
.PHONY : librtsp/sdp/sdp-aac.i

# target to preprocess a source file
librtsp/sdp/sdp-aac.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-aac.c.i
.PHONY : librtsp/sdp/sdp-aac.c.i

librtsp/sdp/sdp-aac.s: librtsp/sdp/sdp-aac.c.s
.PHONY : librtsp/sdp/sdp-aac.s

# target to generate assembly for a file
librtsp/sdp/sdp-aac.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-aac.c.s
.PHONY : librtsp/sdp/sdp-aac.c.s

librtsp/sdp/sdp-av1.o: librtsp/sdp/sdp-av1.c.o
.PHONY : librtsp/sdp/sdp-av1.o

# target to build an object file
librtsp/sdp/sdp-av1.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-av1.c.o
.PHONY : librtsp/sdp/sdp-av1.c.o

librtsp/sdp/sdp-av1.i: librtsp/sdp/sdp-av1.c.i
.PHONY : librtsp/sdp/sdp-av1.i

# target to preprocess a source file
librtsp/sdp/sdp-av1.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-av1.c.i
.PHONY : librtsp/sdp/sdp-av1.c.i

librtsp/sdp/sdp-av1.s: librtsp/sdp/sdp-av1.c.s
.PHONY : librtsp/sdp/sdp-av1.s

# target to generate assembly for a file
librtsp/sdp/sdp-av1.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-av1.c.s
.PHONY : librtsp/sdp/sdp-av1.c.s

librtsp/sdp/sdp-fmtp-load.o: librtsp/sdp/sdp-fmtp-load.c.o
.PHONY : librtsp/sdp/sdp-fmtp-load.o

# target to build an object file
librtsp/sdp/sdp-fmtp-load.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-fmtp-load.c.o
.PHONY : librtsp/sdp/sdp-fmtp-load.c.o

librtsp/sdp/sdp-fmtp-load.i: librtsp/sdp/sdp-fmtp-load.c.i
.PHONY : librtsp/sdp/sdp-fmtp-load.i

# target to preprocess a source file
librtsp/sdp/sdp-fmtp-load.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-fmtp-load.c.i
.PHONY : librtsp/sdp/sdp-fmtp-load.c.i

librtsp/sdp/sdp-fmtp-load.s: librtsp/sdp/sdp-fmtp-load.c.s
.PHONY : librtsp/sdp/sdp-fmtp-load.s

# target to generate assembly for a file
librtsp/sdp/sdp-fmtp-load.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-fmtp-load.c.s
.PHONY : librtsp/sdp/sdp-fmtp-load.c.s

librtsp/sdp/sdp-g7xx.o: librtsp/sdp/sdp-g7xx.c.o
.PHONY : librtsp/sdp/sdp-g7xx.o

# target to build an object file
librtsp/sdp/sdp-g7xx.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-g7xx.c.o
.PHONY : librtsp/sdp/sdp-g7xx.c.o

librtsp/sdp/sdp-g7xx.i: librtsp/sdp/sdp-g7xx.c.i
.PHONY : librtsp/sdp/sdp-g7xx.i

# target to preprocess a source file
librtsp/sdp/sdp-g7xx.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-g7xx.c.i
.PHONY : librtsp/sdp/sdp-g7xx.c.i

librtsp/sdp/sdp-g7xx.s: librtsp/sdp/sdp-g7xx.c.s
.PHONY : librtsp/sdp/sdp-g7xx.s

# target to generate assembly for a file
librtsp/sdp/sdp-g7xx.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-g7xx.c.s
.PHONY : librtsp/sdp/sdp-g7xx.c.s

librtsp/sdp/sdp-h264.o: librtsp/sdp/sdp-h264.c.o
.PHONY : librtsp/sdp/sdp-h264.o

# target to build an object file
librtsp/sdp/sdp-h264.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h264.c.o
.PHONY : librtsp/sdp/sdp-h264.c.o

librtsp/sdp/sdp-h264.i: librtsp/sdp/sdp-h264.c.i
.PHONY : librtsp/sdp/sdp-h264.i

# target to preprocess a source file
librtsp/sdp/sdp-h264.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h264.c.i
.PHONY : librtsp/sdp/sdp-h264.c.i

librtsp/sdp/sdp-h264.s: librtsp/sdp/sdp-h264.c.s
.PHONY : librtsp/sdp/sdp-h264.s

# target to generate assembly for a file
librtsp/sdp/sdp-h264.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h264.c.s
.PHONY : librtsp/sdp/sdp-h264.c.s

librtsp/sdp/sdp-h265.o: librtsp/sdp/sdp-h265.c.o
.PHONY : librtsp/sdp/sdp-h265.o

# target to build an object file
librtsp/sdp/sdp-h265.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h265.c.o
.PHONY : librtsp/sdp/sdp-h265.c.o

librtsp/sdp/sdp-h265.i: librtsp/sdp/sdp-h265.c.i
.PHONY : librtsp/sdp/sdp-h265.i

# target to preprocess a source file
librtsp/sdp/sdp-h265.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h265.c.i
.PHONY : librtsp/sdp/sdp-h265.c.i

librtsp/sdp/sdp-h265.s: librtsp/sdp/sdp-h265.c.s
.PHONY : librtsp/sdp/sdp-h265.s

# target to generate assembly for a file
librtsp/sdp/sdp-h265.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h265.c.s
.PHONY : librtsp/sdp/sdp-h265.c.s

librtsp/sdp/sdp-h266.o: librtsp/sdp/sdp-h266.c.o
.PHONY : librtsp/sdp/sdp-h266.o

# target to build an object file
librtsp/sdp/sdp-h266.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h266.c.o
.PHONY : librtsp/sdp/sdp-h266.c.o

librtsp/sdp/sdp-h266.i: librtsp/sdp/sdp-h266.c.i
.PHONY : librtsp/sdp/sdp-h266.i

# target to preprocess a source file
librtsp/sdp/sdp-h266.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h266.c.i
.PHONY : librtsp/sdp/sdp-h266.c.i

librtsp/sdp/sdp-h266.s: librtsp/sdp/sdp-h266.c.s
.PHONY : librtsp/sdp/sdp-h266.s

# target to generate assembly for a file
librtsp/sdp/sdp-h266.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-h266.c.s
.PHONY : librtsp/sdp/sdp-h266.c.s

librtsp/sdp/sdp-mpeg2.o: librtsp/sdp/sdp-mpeg2.c.o
.PHONY : librtsp/sdp/sdp-mpeg2.o

# target to build an object file
librtsp/sdp/sdp-mpeg2.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-mpeg2.c.o
.PHONY : librtsp/sdp/sdp-mpeg2.c.o

librtsp/sdp/sdp-mpeg2.i: librtsp/sdp/sdp-mpeg2.c.i
.PHONY : librtsp/sdp/sdp-mpeg2.i

# target to preprocess a source file
librtsp/sdp/sdp-mpeg2.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-mpeg2.c.i
.PHONY : librtsp/sdp/sdp-mpeg2.c.i

librtsp/sdp/sdp-mpeg2.s: librtsp/sdp/sdp-mpeg2.c.s
.PHONY : librtsp/sdp/sdp-mpeg2.s

# target to generate assembly for a file
librtsp/sdp/sdp-mpeg2.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-mpeg2.c.s
.PHONY : librtsp/sdp/sdp-mpeg2.c.s

librtsp/sdp/sdp-mpeg4.o: librtsp/sdp/sdp-mpeg4.c.o
.PHONY : librtsp/sdp/sdp-mpeg4.o

# target to build an object file
librtsp/sdp/sdp-mpeg4.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-mpeg4.c.o
.PHONY : librtsp/sdp/sdp-mpeg4.c.o

librtsp/sdp/sdp-mpeg4.i: librtsp/sdp/sdp-mpeg4.c.i
.PHONY : librtsp/sdp/sdp-mpeg4.i

# target to preprocess a source file
librtsp/sdp/sdp-mpeg4.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-mpeg4.c.i
.PHONY : librtsp/sdp/sdp-mpeg4.c.i

librtsp/sdp/sdp-mpeg4.s: librtsp/sdp/sdp-mpeg4.c.s
.PHONY : librtsp/sdp/sdp-mpeg4.s

# target to generate assembly for a file
librtsp/sdp/sdp-mpeg4.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-mpeg4.c.s
.PHONY : librtsp/sdp/sdp-mpeg4.c.s

librtsp/sdp/sdp-opus.o: librtsp/sdp/sdp-opus.c.o
.PHONY : librtsp/sdp/sdp-opus.o

# target to build an object file
librtsp/sdp/sdp-opus.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-opus.c.o
.PHONY : librtsp/sdp/sdp-opus.c.o

librtsp/sdp/sdp-opus.i: librtsp/sdp/sdp-opus.c.i
.PHONY : librtsp/sdp/sdp-opus.i

# target to preprocess a source file
librtsp/sdp/sdp-opus.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-opus.c.i
.PHONY : librtsp/sdp/sdp-opus.c.i

librtsp/sdp/sdp-opus.s: librtsp/sdp/sdp-opus.c.s
.PHONY : librtsp/sdp/sdp-opus.s

# target to generate assembly for a file
librtsp/sdp/sdp-opus.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-opus.c.s
.PHONY : librtsp/sdp/sdp-opus.c.s

librtsp/sdp/sdp-payload.o: librtsp/sdp/sdp-payload.c.o
.PHONY : librtsp/sdp/sdp-payload.o

# target to build an object file
librtsp/sdp/sdp-payload.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-payload.c.o
.PHONY : librtsp/sdp/sdp-payload.c.o

librtsp/sdp/sdp-payload.i: librtsp/sdp/sdp-payload.c.i
.PHONY : librtsp/sdp/sdp-payload.i

# target to preprocess a source file
librtsp/sdp/sdp-payload.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-payload.c.i
.PHONY : librtsp/sdp/sdp-payload.c.i

librtsp/sdp/sdp-payload.s: librtsp/sdp/sdp-payload.c.s
.PHONY : librtsp/sdp/sdp-payload.s

# target to generate assembly for a file
librtsp/sdp/sdp-payload.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-payload.c.s
.PHONY : librtsp/sdp/sdp-payload.c.s

librtsp/sdp/sdp-vpx.o: librtsp/sdp/sdp-vpx.c.o
.PHONY : librtsp/sdp/sdp-vpx.o

# target to build an object file
librtsp/sdp/sdp-vpx.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-vpx.c.o
.PHONY : librtsp/sdp/sdp-vpx.c.o

librtsp/sdp/sdp-vpx.i: librtsp/sdp/sdp-vpx.c.i
.PHONY : librtsp/sdp/sdp-vpx.i

# target to preprocess a source file
librtsp/sdp/sdp-vpx.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-vpx.c.i
.PHONY : librtsp/sdp/sdp-vpx.c.i

librtsp/sdp/sdp-vpx.s: librtsp/sdp/sdp-vpx.c.s
.PHONY : librtsp/sdp/sdp-vpx.s

# target to generate assembly for a file
librtsp/sdp/sdp-vpx.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/sdp/sdp-vpx.c.s
.PHONY : librtsp/sdp/sdp-vpx.c.s

librtsp/source/rtsp-header-range.o: librtsp/source/rtsp-header-range.c.o
.PHONY : librtsp/source/rtsp-header-range.o

# target to build an object file
librtsp/source/rtsp-header-range.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.o
.PHONY : librtsp/source/rtsp-header-range.c.o

librtsp/source/rtsp-header-range.i: librtsp/source/rtsp-header-range.c.i
.PHONY : librtsp/source/rtsp-header-range.i

# target to preprocess a source file
librtsp/source/rtsp-header-range.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.i
.PHONY : librtsp/source/rtsp-header-range.c.i

librtsp/source/rtsp-header-range.s: librtsp/source/rtsp-header-range.c.s
.PHONY : librtsp/source/rtsp-header-range.s

# target to generate assembly for a file
librtsp/source/rtsp-header-range.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-range.c.s
.PHONY : librtsp/source/rtsp-header-range.c.s

librtsp/source/rtsp-header-rtp-info.o: librtsp/source/rtsp-header-rtp-info.c.o
.PHONY : librtsp/source/rtsp-header-rtp-info.o

# target to build an object file
librtsp/source/rtsp-header-rtp-info.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.o
.PHONY : librtsp/source/rtsp-header-rtp-info.c.o

librtsp/source/rtsp-header-rtp-info.i: librtsp/source/rtsp-header-rtp-info.c.i
.PHONY : librtsp/source/rtsp-header-rtp-info.i

# target to preprocess a source file
librtsp/source/rtsp-header-rtp-info.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.i
.PHONY : librtsp/source/rtsp-header-rtp-info.c.i

librtsp/source/rtsp-header-rtp-info.s: librtsp/source/rtsp-header-rtp-info.c.s
.PHONY : librtsp/source/rtsp-header-rtp-info.s

# target to generate assembly for a file
librtsp/source/rtsp-header-rtp-info.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-rtp-info.c.s
.PHONY : librtsp/source/rtsp-header-rtp-info.c.s

librtsp/source/rtsp-header-session.o: librtsp/source/rtsp-header-session.c.o
.PHONY : librtsp/source/rtsp-header-session.o

# target to build an object file
librtsp/source/rtsp-header-session.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.o
.PHONY : librtsp/source/rtsp-header-session.c.o

librtsp/source/rtsp-header-session.i: librtsp/source/rtsp-header-session.c.i
.PHONY : librtsp/source/rtsp-header-session.i

# target to preprocess a source file
librtsp/source/rtsp-header-session.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.i
.PHONY : librtsp/source/rtsp-header-session.c.i

librtsp/source/rtsp-header-session.s: librtsp/source/rtsp-header-session.c.s
.PHONY : librtsp/source/rtsp-header-session.s

# target to generate assembly for a file
librtsp/source/rtsp-header-session.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-session.c.s
.PHONY : librtsp/source/rtsp-header-session.c.s

librtsp/source/rtsp-header-transport.o: librtsp/source/rtsp-header-transport.c.o
.PHONY : librtsp/source/rtsp-header-transport.o

# target to build an object file
librtsp/source/rtsp-header-transport.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.o
.PHONY : librtsp/source/rtsp-header-transport.c.o

librtsp/source/rtsp-header-transport.i: librtsp/source/rtsp-header-transport.c.i
.PHONY : librtsp/source/rtsp-header-transport.i

# target to preprocess a source file
librtsp/source/rtsp-header-transport.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.i
.PHONY : librtsp/source/rtsp-header-transport.c.i

librtsp/source/rtsp-header-transport.s: librtsp/source/rtsp-header-transport.c.s
.PHONY : librtsp/source/rtsp-header-transport.s

# target to generate assembly for a file
librtsp/source/rtsp-header-transport.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-header-transport.c.s
.PHONY : librtsp/source/rtsp-header-transport.c.s

librtsp/source/rtsp-media.o: librtsp/source/rtsp-media.c.o
.PHONY : librtsp/source/rtsp-media.o

# target to build an object file
librtsp/source/rtsp-media.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.o
.PHONY : librtsp/source/rtsp-media.c.o

librtsp/source/rtsp-media.i: librtsp/source/rtsp-media.c.i
.PHONY : librtsp/source/rtsp-media.i

# target to preprocess a source file
librtsp/source/rtsp-media.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.i
.PHONY : librtsp/source/rtsp-media.c.i

librtsp/source/rtsp-media.s: librtsp/source/rtsp-media.c.s
.PHONY : librtsp/source/rtsp-media.s

# target to generate assembly for a file
librtsp/source/rtsp-media.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-media.c.s
.PHONY : librtsp/source/rtsp-media.c.s

librtsp/source/rtsp-multicast.o: librtsp/source/rtsp-multicast.c.o
.PHONY : librtsp/source/rtsp-multicast.o

# target to build an object file
librtsp/source/rtsp-multicast.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.o
.PHONY : librtsp/source/rtsp-multicast.c.o

librtsp/source/rtsp-multicast.i: librtsp/source/rtsp-multicast.c.i
.PHONY : librtsp/source/rtsp-multicast.i

# target to preprocess a source file
librtsp/source/rtsp-multicast.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.i
.PHONY : librtsp/source/rtsp-multicast.c.i

librtsp/source/rtsp-multicast.s: librtsp/source/rtsp-multicast.c.s
.PHONY : librtsp/source/rtsp-multicast.s

# target to generate assembly for a file
librtsp/source/rtsp-multicast.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-multicast.c.s
.PHONY : librtsp/source/rtsp-multicast.c.s

librtsp/source/rtsp-reason.o: librtsp/source/rtsp-reason.c.o
.PHONY : librtsp/source/rtsp-reason.o

# target to build an object file
librtsp/source/rtsp-reason.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.o
.PHONY : librtsp/source/rtsp-reason.c.o

librtsp/source/rtsp-reason.i: librtsp/source/rtsp-reason.c.i
.PHONY : librtsp/source/rtsp-reason.i

# target to preprocess a source file
librtsp/source/rtsp-reason.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.i
.PHONY : librtsp/source/rtsp-reason.c.i

librtsp/source/rtsp-reason.s: librtsp/source/rtsp-reason.c.s
.PHONY : librtsp/source/rtsp-reason.s

# target to generate assembly for a file
librtsp/source/rtsp-reason.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/rtsp-reason.c.s
.PHONY : librtsp/source/rtsp-reason.c.s

librtsp/source/sdp-options.o: librtsp/source/sdp-options.c.o
.PHONY : librtsp/source/sdp-options.o

# target to build an object file
librtsp/source/sdp-options.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/sdp-options.c.o
.PHONY : librtsp/source/sdp-options.c.o

librtsp/source/sdp-options.i: librtsp/source/sdp-options.c.i
.PHONY : librtsp/source/sdp-options.i

# target to preprocess a source file
librtsp/source/sdp-options.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/sdp-options.c.i
.PHONY : librtsp/source/sdp-options.c.i

librtsp/source/sdp-options.s: librtsp/source/sdp-options.c.s
.PHONY : librtsp/source/sdp-options.s

# target to generate assembly for a file
librtsp/source/sdp-options.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/sdp-options.c.s
.PHONY : librtsp/source/sdp-options.c.s

librtsp/source/server/rtsp-server-announce.o: librtsp/source/server/rtsp-server-announce.c.o
.PHONY : librtsp/source/server/rtsp-server-announce.o

# target to build an object file
librtsp/source/server/rtsp-server-announce.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.o
.PHONY : librtsp/source/server/rtsp-server-announce.c.o

librtsp/source/server/rtsp-server-announce.i: librtsp/source/server/rtsp-server-announce.c.i
.PHONY : librtsp/source/server/rtsp-server-announce.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-announce.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.i
.PHONY : librtsp/source/server/rtsp-server-announce.c.i

librtsp/source/server/rtsp-server-announce.s: librtsp/source/server/rtsp-server-announce.c.s
.PHONY : librtsp/source/server/rtsp-server-announce.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-announce.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-announce.c.s
.PHONY : librtsp/source/server/rtsp-server-announce.c.s

librtsp/source/server/rtsp-server-describe.o: librtsp/source/server/rtsp-server-describe.c.o
.PHONY : librtsp/source/server/rtsp-server-describe.o

# target to build an object file
librtsp/source/server/rtsp-server-describe.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.o
.PHONY : librtsp/source/server/rtsp-server-describe.c.o

librtsp/source/server/rtsp-server-describe.i: librtsp/source/server/rtsp-server-describe.c.i
.PHONY : librtsp/source/server/rtsp-server-describe.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-describe.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.i
.PHONY : librtsp/source/server/rtsp-server-describe.c.i

librtsp/source/server/rtsp-server-describe.s: librtsp/source/server/rtsp-server-describe.c.s
.PHONY : librtsp/source/server/rtsp-server-describe.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-describe.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-describe.c.s
.PHONY : librtsp/source/server/rtsp-server-describe.c.s

librtsp/source/server/rtsp-server-handler.o: librtsp/source/server/rtsp-server-handler.c.o
.PHONY : librtsp/source/server/rtsp-server-handler.o

# target to build an object file
librtsp/source/server/rtsp-server-handler.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.o
.PHONY : librtsp/source/server/rtsp-server-handler.c.o

librtsp/source/server/rtsp-server-handler.i: librtsp/source/server/rtsp-server-handler.c.i
.PHONY : librtsp/source/server/rtsp-server-handler.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-handler.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.i
.PHONY : librtsp/source/server/rtsp-server-handler.c.i

librtsp/source/server/rtsp-server-handler.s: librtsp/source/server/rtsp-server-handler.c.s
.PHONY : librtsp/source/server/rtsp-server-handler.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-handler.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-handler.c.s
.PHONY : librtsp/source/server/rtsp-server-handler.c.s

librtsp/source/server/rtsp-server-options.o: librtsp/source/server/rtsp-server-options.c.o
.PHONY : librtsp/source/server/rtsp-server-options.o

# target to build an object file
librtsp/source/server/rtsp-server-options.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.o
.PHONY : librtsp/source/server/rtsp-server-options.c.o

librtsp/source/server/rtsp-server-options.i: librtsp/source/server/rtsp-server-options.c.i
.PHONY : librtsp/source/server/rtsp-server-options.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-options.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.i
.PHONY : librtsp/source/server/rtsp-server-options.c.i

librtsp/source/server/rtsp-server-options.s: librtsp/source/server/rtsp-server-options.c.s
.PHONY : librtsp/source/server/rtsp-server-options.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-options.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-options.c.s
.PHONY : librtsp/source/server/rtsp-server-options.c.s

librtsp/source/server/rtsp-server-parameter.o: librtsp/source/server/rtsp-server-parameter.c.o
.PHONY : librtsp/source/server/rtsp-server-parameter.o

# target to build an object file
librtsp/source/server/rtsp-server-parameter.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.o
.PHONY : librtsp/source/server/rtsp-server-parameter.c.o

librtsp/source/server/rtsp-server-parameter.i: librtsp/source/server/rtsp-server-parameter.c.i
.PHONY : librtsp/source/server/rtsp-server-parameter.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-parameter.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.i
.PHONY : librtsp/source/server/rtsp-server-parameter.c.i

librtsp/source/server/rtsp-server-parameter.s: librtsp/source/server/rtsp-server-parameter.c.s
.PHONY : librtsp/source/server/rtsp-server-parameter.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-parameter.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-parameter.c.s
.PHONY : librtsp/source/server/rtsp-server-parameter.c.s

librtsp/source/server/rtsp-server-pause.o: librtsp/source/server/rtsp-server-pause.c.o
.PHONY : librtsp/source/server/rtsp-server-pause.o

# target to build an object file
librtsp/source/server/rtsp-server-pause.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.o
.PHONY : librtsp/source/server/rtsp-server-pause.c.o

librtsp/source/server/rtsp-server-pause.i: librtsp/source/server/rtsp-server-pause.c.i
.PHONY : librtsp/source/server/rtsp-server-pause.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-pause.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.i
.PHONY : librtsp/source/server/rtsp-server-pause.c.i

librtsp/source/server/rtsp-server-pause.s: librtsp/source/server/rtsp-server-pause.c.s
.PHONY : librtsp/source/server/rtsp-server-pause.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-pause.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-pause.c.s
.PHONY : librtsp/source/server/rtsp-server-pause.c.s

librtsp/source/server/rtsp-server-play.o: librtsp/source/server/rtsp-server-play.c.o
.PHONY : librtsp/source/server/rtsp-server-play.o

# target to build an object file
librtsp/source/server/rtsp-server-play.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.o
.PHONY : librtsp/source/server/rtsp-server-play.c.o

librtsp/source/server/rtsp-server-play.i: librtsp/source/server/rtsp-server-play.c.i
.PHONY : librtsp/source/server/rtsp-server-play.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-play.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.i
.PHONY : librtsp/source/server/rtsp-server-play.c.i

librtsp/source/server/rtsp-server-play.s: librtsp/source/server/rtsp-server-play.c.s
.PHONY : librtsp/source/server/rtsp-server-play.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-play.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-play.c.s
.PHONY : librtsp/source/server/rtsp-server-play.c.s

librtsp/source/server/rtsp-server-record.o: librtsp/source/server/rtsp-server-record.c.o
.PHONY : librtsp/source/server/rtsp-server-record.o

# target to build an object file
librtsp/source/server/rtsp-server-record.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.o
.PHONY : librtsp/source/server/rtsp-server-record.c.o

librtsp/source/server/rtsp-server-record.i: librtsp/source/server/rtsp-server-record.c.i
.PHONY : librtsp/source/server/rtsp-server-record.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-record.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.i
.PHONY : librtsp/source/server/rtsp-server-record.c.i

librtsp/source/server/rtsp-server-record.s: librtsp/source/server/rtsp-server-record.c.s
.PHONY : librtsp/source/server/rtsp-server-record.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-record.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-record.c.s
.PHONY : librtsp/source/server/rtsp-server-record.c.s

librtsp/source/server/rtsp-server-setup.o: librtsp/source/server/rtsp-server-setup.c.o
.PHONY : librtsp/source/server/rtsp-server-setup.o

# target to build an object file
librtsp/source/server/rtsp-server-setup.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.o
.PHONY : librtsp/source/server/rtsp-server-setup.c.o

librtsp/source/server/rtsp-server-setup.i: librtsp/source/server/rtsp-server-setup.c.i
.PHONY : librtsp/source/server/rtsp-server-setup.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-setup.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.i
.PHONY : librtsp/source/server/rtsp-server-setup.c.i

librtsp/source/server/rtsp-server-setup.s: librtsp/source/server/rtsp-server-setup.c.s
.PHONY : librtsp/source/server/rtsp-server-setup.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-setup.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-setup.c.s
.PHONY : librtsp/source/server/rtsp-server-setup.c.s

librtsp/source/server/rtsp-server-teardown.o: librtsp/source/server/rtsp-server-teardown.c.o
.PHONY : librtsp/source/server/rtsp-server-teardown.o

# target to build an object file
librtsp/source/server/rtsp-server-teardown.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.o
.PHONY : librtsp/source/server/rtsp-server-teardown.c.o

librtsp/source/server/rtsp-server-teardown.i: librtsp/source/server/rtsp-server-teardown.c.i
.PHONY : librtsp/source/server/rtsp-server-teardown.i

# target to preprocess a source file
librtsp/source/server/rtsp-server-teardown.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.i
.PHONY : librtsp/source/server/rtsp-server-teardown.c.i

librtsp/source/server/rtsp-server-teardown.s: librtsp/source/server/rtsp-server-teardown.c.s
.PHONY : librtsp/source/server/rtsp-server-teardown.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server-teardown.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server-teardown.c.s
.PHONY : librtsp/source/server/rtsp-server-teardown.c.s

librtsp/source/server/rtsp-server.o: librtsp/source/server/rtsp-server.c.o
.PHONY : librtsp/source/server/rtsp-server.o

# target to build an object file
librtsp/source/server/rtsp-server.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.o
.PHONY : librtsp/source/server/rtsp-server.c.o

librtsp/source/server/rtsp-server.i: librtsp/source/server/rtsp-server.c.i
.PHONY : librtsp/source/server/rtsp-server.i

# target to preprocess a source file
librtsp/source/server/rtsp-server.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.i
.PHONY : librtsp/source/server/rtsp-server.c.i

librtsp/source/server/rtsp-server.s: librtsp/source/server/rtsp-server.c.s
.PHONY : librtsp/source/server/rtsp-server.s

# target to generate assembly for a file
librtsp/source/server/rtsp-server.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/server/rtsp-server.c.s
.PHONY : librtsp/source/server/rtsp-server.c.s

librtsp/source/utils/rtp-sender.o: librtsp/source/utils/rtp-sender.c.o
.PHONY : librtsp/source/utils/rtp-sender.o

# target to build an object file
librtsp/source/utils/rtp-sender.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.o
.PHONY : librtsp/source/utils/rtp-sender.c.o

librtsp/source/utils/rtp-sender.i: librtsp/source/utils/rtp-sender.c.i
.PHONY : librtsp/source/utils/rtp-sender.i

# target to preprocess a source file
librtsp/source/utils/rtp-sender.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.i
.PHONY : librtsp/source/utils/rtp-sender.c.i

librtsp/source/utils/rtp-sender.s: librtsp/source/utils/rtp-sender.c.s
.PHONY : librtsp/source/utils/rtp-sender.s

# target to generate assembly for a file
librtsp/source/utils/rtp-sender.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtp-sender.c.s
.PHONY : librtsp/source/utils/rtp-sender.c.s

librtsp/source/utils/rtsp-demuxer.o: librtsp/source/utils/rtsp-demuxer.c.o
.PHONY : librtsp/source/utils/rtsp-demuxer.o

# target to build an object file
librtsp/source/utils/rtsp-demuxer.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.o
.PHONY : librtsp/source/utils/rtsp-demuxer.c.o

librtsp/source/utils/rtsp-demuxer.i: librtsp/source/utils/rtsp-demuxer.c.i
.PHONY : librtsp/source/utils/rtsp-demuxer.i

# target to preprocess a source file
librtsp/source/utils/rtsp-demuxer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.i
.PHONY : librtsp/source/utils/rtsp-demuxer.c.i

librtsp/source/utils/rtsp-demuxer.s: librtsp/source/utils/rtsp-demuxer.c.s
.PHONY : librtsp/source/utils/rtsp-demuxer.s

# target to generate assembly for a file
librtsp/source/utils/rtsp-demuxer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-demuxer.c.s
.PHONY : librtsp/source/utils/rtsp-demuxer.c.s

librtsp/source/utils/rtsp-muxer.o: librtsp/source/utils/rtsp-muxer.c.o
.PHONY : librtsp/source/utils/rtsp-muxer.o

# target to build an object file
librtsp/source/utils/rtsp-muxer.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.o
.PHONY : librtsp/source/utils/rtsp-muxer.c.o

librtsp/source/utils/rtsp-muxer.i: librtsp/source/utils/rtsp-muxer.c.i
.PHONY : librtsp/source/utils/rtsp-muxer.i

# target to preprocess a source file
librtsp/source/utils/rtsp-muxer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.i
.PHONY : librtsp/source/utils/rtsp-muxer.c.i

librtsp/source/utils/rtsp-muxer.s: librtsp/source/utils/rtsp-muxer.c.s
.PHONY : librtsp/source/utils/rtsp-muxer.s

# target to generate assembly for a file
librtsp/source/utils/rtsp-muxer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp.dir/build.make CMakeFiles/rtsp.dir/librtsp/source/utils/rtsp-muxer.c.s
.PHONY : librtsp/source/utils/rtsp-muxer.c.s

mpeg4-aac.o: mpeg4-aac.c.o
.PHONY : mpeg4-aac.o

# target to build an object file
mpeg4-aac.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-aac.c.o
.PHONY : mpeg4-aac.c.o

mpeg4-aac.i: mpeg4-aac.c.i
.PHONY : mpeg4-aac.i

# target to preprocess a source file
mpeg4-aac.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-aac.c.i
.PHONY : mpeg4-aac.c.i

mpeg4-aac.s: mpeg4-aac.c.s
.PHONY : mpeg4-aac.s

# target to generate assembly for a file
mpeg4-aac.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-aac.c.s
.PHONY : mpeg4-aac.c.s

mpeg4-avc.o: mpeg4-avc.c.o
.PHONY : mpeg4-avc.o

# target to build an object file
mpeg4-avc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-avc.c.o
.PHONY : mpeg4-avc.c.o

mpeg4-avc.i: mpeg4-avc.c.i
.PHONY : mpeg4-avc.i

# target to preprocess a source file
mpeg4-avc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-avc.c.i
.PHONY : mpeg4-avc.c.i

mpeg4-avc.s: mpeg4-avc.c.s
.PHONY : mpeg4-avc.s

# target to generate assembly for a file
mpeg4-avc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-avc.c.s
.PHONY : mpeg4-avc.c.s

mpeg4-bits.o: mpeg4-bits.c.o
.PHONY : mpeg4-bits.o

# target to build an object file
mpeg4-bits.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-bits.c.o
.PHONY : mpeg4-bits.c.o

mpeg4-bits.i: mpeg4-bits.c.i
.PHONY : mpeg4-bits.i

# target to preprocess a source file
mpeg4-bits.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-bits.c.i
.PHONY : mpeg4-bits.c.i

mpeg4-bits.s: mpeg4-bits.c.s
.PHONY : mpeg4-bits.s

# target to generate assembly for a file
mpeg4-bits.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-bits.c.s
.PHONY : mpeg4-bits.c.s

mpeg4-hevc.o: mpeg4-hevc.c.o
.PHONY : mpeg4-hevc.o

# target to build an object file
mpeg4-hevc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-hevc.c.o
.PHONY : mpeg4-hevc.c.o

mpeg4-hevc.i: mpeg4-hevc.c.i
.PHONY : mpeg4-hevc.i

# target to preprocess a source file
mpeg4-hevc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-hevc.c.i
.PHONY : mpeg4-hevc.c.i

mpeg4-hevc.s: mpeg4-hevc.c.s
.PHONY : mpeg4-hevc.s

# target to generate assembly for a file
mpeg4-hevc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-hevc.c.s
.PHONY : mpeg4-hevc.c.s

mpeg4-vvc.o: mpeg4-vvc.c.o
.PHONY : mpeg4-vvc.o

# target to build an object file
mpeg4-vvc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-vvc.c.o
.PHONY : mpeg4-vvc.c.o

mpeg4-vvc.i: mpeg4-vvc.c.i
.PHONY : mpeg4-vvc.i

# target to preprocess a source file
mpeg4-vvc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-vvc.c.i
.PHONY : mpeg4-vvc.c.i

mpeg4-vvc.s: mpeg4-vvc.c.s
.PHONY : mpeg4-vvc.s

# target to generate assembly for a file
mpeg4-vvc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/mpeg4-vvc.c.s
.PHONY : mpeg4-vvc.c.s

rtp-tcp-transport.o: rtp-tcp-transport.cpp.o
.PHONY : rtp-tcp-transport.o

# target to build an object file
rtp-tcp-transport.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o
.PHONY : rtp-tcp-transport.cpp.o

rtp-tcp-transport.i: rtp-tcp-transport.cpp.i
.PHONY : rtp-tcp-transport.i

# target to preprocess a source file
rtp-tcp-transport.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.i
.PHONY : rtp-tcp-transport.cpp.i

rtp-tcp-transport.s: rtp-tcp-transport.cpp.s
.PHONY : rtp-tcp-transport.s

# target to generate assembly for a file
rtp-tcp-transport.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.s
.PHONY : rtp-tcp-transport.cpp.s

rtp-udp-transport.o: rtp-udp-transport.cpp.o
.PHONY : rtp-udp-transport.o

# target to build an object file
rtp-udp-transport.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o
.PHONY : rtp-udp-transport.cpp.o

rtp-udp-transport.i: rtp-udp-transport.cpp.i
.PHONY : rtp-udp-transport.i

# target to preprocess a source file
rtp-udp-transport.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.i
.PHONY : rtp-udp-transport.cpp.i

rtp-udp-transport.s: rtp-udp-transport.cpp.s
.PHONY : rtp-udp-transport.s

# target to generate assembly for a file
rtp-udp-transport.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.s
.PHONY : rtp-udp-transport.cpp.s

rtsp-server-test.o: rtsp-server-test.cpp.o
.PHONY : rtsp-server-test.o

# target to build an object file
rtsp-server-test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o
.PHONY : rtsp-server-test.cpp.o

rtsp-server-test.i: rtsp-server-test.cpp.i
.PHONY : rtsp-server-test.i

# target to preprocess a source file
rtsp-server-test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.i
.PHONY : rtsp-server-test.cpp.i

rtsp-server-test.s: rtsp-server-test.cpp.s
.PHONY : rtsp-server-test.s

# target to generate assembly for a file
rtsp-server-test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.s
.PHONY : rtsp-server-test.cpp.s

rtsp-utils.o: rtsp-utils.cpp.o
.PHONY : rtsp-utils.o

# target to build an object file
rtsp-utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o
.PHONY : rtsp-utils.cpp.o

rtsp-utils.i: rtsp-utils.cpp.i
.PHONY : rtsp-utils.i

# target to preprocess a source file
rtsp-utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.i
.PHONY : rtsp-utils.cpp.i

rtsp-utils.s: rtsp-utils.cpp.s
.PHONY : rtsp-utils.s

# target to generate assembly for a file
rtsp-utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.s
.PHONY : rtsp-utils.cpp.s

sys/base64.o: sys/base64.c.o
.PHONY : sys/base64.o

# target to build an object file
sys/base64.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/sys/base64.c.o
.PHONY : sys/base64.c.o

sys/base64.i: sys/base64.c.i
.PHONY : sys/base64.i

# target to preprocess a source file
sys/base64.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/sys/base64.c.i
.PHONY : sys/base64.c.i

sys/base64.s: sys/base64.c.s
.PHONY : sys/base64.s

# target to generate assembly for a file
sys/base64.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/sys/base64.c.s
.PHONY : sys/base64.c.s

sys/ntp-time.o: sys/ntp-time.c.o
.PHONY : sys/ntp-time.o

# target to build an object file
sys/ntp-time.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/sys/ntp-time.c.o
.PHONY : sys/ntp-time.c.o

sys/ntp-time.i: sys/ntp-time.c.i
.PHONY : sys/ntp-time.i

# target to preprocess a source file
sys/ntp-time.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/sys/ntp-time.c.i
.PHONY : sys/ntp-time.c.i

sys/ntp-time.s: sys/ntp-time.c.s
.PHONY : sys/ntp-time.s

# target to generate assembly for a file
sys/ntp-time.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtsp-server-test.dir/build.make CMakeFiles/rtsp-server-test.dir/sys/ntp-time.c.s
.PHONY : sys/ntp-time.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... rtp"
	@echo "... rtsp"
	@echo "... rtsp-server-test"
	@echo "... aom-av1.o"
	@echo "... aom-av1.i"
	@echo "... aom-av1.s"
	@echo "... h264-file-reader.o"
	@echo "... h264-file-reader.i"
	@echo "... h264-file-reader.s"
	@echo "... h264-file-source.o"
	@echo "... h264-file-source.i"
	@echo "... h264-file-source.s"
	@echo "... h265-file-reader.o"
	@echo "... h265-file-reader.i"
	@echo "... h265-file-reader.s"
	@echo "... h265-file-source.o"
	@echo "... h265-file-source.i"
	@echo "... h265-file-source.s"
	@echo "... http-header-auth.o"
	@echo "... http-header-auth.i"
	@echo "... http-header-auth.s"
	@echo "... http-parser.o"
	@echo "... http-parser.i"
	@echo "... http-parser.s"
	@echo "... http-reason.o"
	@echo "... http-reason.i"
	@echo "... http-reason.s"
	@echo "... librtp/payload/rtp-av1-pack.o"
	@echo "... librtp/payload/rtp-av1-pack.i"
	@echo "... librtp/payload/rtp-av1-pack.s"
	@echo "... librtp/payload/rtp-av1-unpack.o"
	@echo "... librtp/payload/rtp-av1-unpack.i"
	@echo "... librtp/payload/rtp-av1-unpack.s"
	@echo "... librtp/payload/rtp-h264-bitstream.o"
	@echo "... librtp/payload/rtp-h264-bitstream.i"
	@echo "... librtp/payload/rtp-h264-bitstream.s"
	@echo "... librtp/payload/rtp-h264-pack.o"
	@echo "... librtp/payload/rtp-h264-pack.i"
	@echo "... librtp/payload/rtp-h264-pack.s"
	@echo "... librtp/payload/rtp-h264-unpack.o"
	@echo "... librtp/payload/rtp-h264-unpack.i"
	@echo "... librtp/payload/rtp-h264-unpack.s"
	@echo "... librtp/payload/rtp-h265-pack.o"
	@echo "... librtp/payload/rtp-h265-pack.i"
	@echo "... librtp/payload/rtp-h265-pack.s"
	@echo "... librtp/payload/rtp-h265-unpack.o"
	@echo "... librtp/payload/rtp-h265-unpack.i"
	@echo "... librtp/payload/rtp-h265-unpack.s"
	@echo "... librtp/payload/rtp-h266-pack.o"
	@echo "... librtp/payload/rtp-h266-pack.i"
	@echo "... librtp/payload/rtp-h266-pack.s"
	@echo "... librtp/payload/rtp-h266-unpack.o"
	@echo "... librtp/payload/rtp-h266-unpack.i"
	@echo "... librtp/payload/rtp-h266-unpack.s"
	@echo "... librtp/payload/rtp-mp4a-latm-pack.o"
	@echo "... librtp/payload/rtp-mp4a-latm-pack.i"
	@echo "... librtp/payload/rtp-mp4a-latm-pack.s"
	@echo "... librtp/payload/rtp-mp4a-latm-unpack.o"
	@echo "... librtp/payload/rtp-mp4a-latm-unpack.i"
	@echo "... librtp/payload/rtp-mp4a-latm-unpack.s"
	@echo "... librtp/payload/rtp-mp4v-es-pack.o"
	@echo "... librtp/payload/rtp-mp4v-es-pack.i"
	@echo "... librtp/payload/rtp-mp4v-es-pack.s"
	@echo "... librtp/payload/rtp-mp4v-es-unpack.o"
	@echo "... librtp/payload/rtp-mp4v-es-unpack.i"
	@echo "... librtp/payload/rtp-mp4v-es-unpack.s"
	@echo "... librtp/payload/rtp-mpeg1or2es-pack.o"
	@echo "... librtp/payload/rtp-mpeg1or2es-pack.i"
	@echo "... librtp/payload/rtp-mpeg1or2es-pack.s"
	@echo "... librtp/payload/rtp-mpeg1or2es-unpack.o"
	@echo "... librtp/payload/rtp-mpeg1or2es-unpack.i"
	@echo "... librtp/payload/rtp-mpeg1or2es-unpack.s"
	@echo "... librtp/payload/rtp-mpeg4-generic-pack.o"
	@echo "... librtp/payload/rtp-mpeg4-generic-pack.i"
	@echo "... librtp/payload/rtp-mpeg4-generic-pack.s"
	@echo "... librtp/payload/rtp-mpeg4-generic-unpack.o"
	@echo "... librtp/payload/rtp-mpeg4-generic-unpack.i"
	@echo "... librtp/payload/rtp-mpeg4-generic-unpack.s"
	@echo "... librtp/payload/rtp-pack.o"
	@echo "... librtp/payload/rtp-pack.i"
	@echo "... librtp/payload/rtp-pack.s"
	@echo "... librtp/payload/rtp-payload-helper.o"
	@echo "... librtp/payload/rtp-payload-helper.i"
	@echo "... librtp/payload/rtp-payload-helper.s"
	@echo "... librtp/payload/rtp-payload.o"
	@echo "... librtp/payload/rtp-payload.i"
	@echo "... librtp/payload/rtp-payload.s"
	@echo "... librtp/payload/rtp-ps-unpack.o"
	@echo "... librtp/payload/rtp-ps-unpack.i"
	@echo "... librtp/payload/rtp-ps-unpack.s"
	@echo "... librtp/payload/rtp-ts-pack.o"
	@echo "... librtp/payload/rtp-ts-pack.i"
	@echo "... librtp/payload/rtp-ts-pack.s"
	@echo "... librtp/payload/rtp-ts-unpack.o"
	@echo "... librtp/payload/rtp-ts-unpack.i"
	@echo "... librtp/payload/rtp-ts-unpack.s"
	@echo "... librtp/payload/rtp-unpack.o"
	@echo "... librtp/payload/rtp-unpack.i"
	@echo "... librtp/payload/rtp-unpack.s"
	@echo "... librtp/payload/rtp-vp8-pack.o"
	@echo "... librtp/payload/rtp-vp8-pack.i"
	@echo "... librtp/payload/rtp-vp8-pack.s"
	@echo "... librtp/payload/rtp-vp8-unpack.o"
	@echo "... librtp/payload/rtp-vp8-unpack.i"
	@echo "... librtp/payload/rtp-vp8-unpack.s"
	@echo "... librtp/payload/rtp-vp9-pack.o"
	@echo "... librtp/payload/rtp-vp9-pack.i"
	@echo "... librtp/payload/rtp-vp9-pack.s"
	@echo "... librtp/payload/rtp-vp9-unpack.o"
	@echo "... librtp/payload/rtp-vp9-unpack.i"
	@echo "... librtp/payload/rtp-vp9-unpack.s"
	@echo "... librtp/source/rtcp-app.o"
	@echo "... librtp/source/rtcp-app.i"
	@echo "... librtp/source/rtcp-app.s"
	@echo "... librtp/source/rtcp-bye.o"
	@echo "... librtp/source/rtcp-bye.i"
	@echo "... librtp/source/rtcp-bye.s"
	@echo "... librtp/source/rtcp-interval.o"
	@echo "... librtp/source/rtcp-interval.i"
	@echo "... librtp/source/rtcp-interval.s"
	@echo "... librtp/source/rtcp-psfb.o"
	@echo "... librtp/source/rtcp-psfb.i"
	@echo "... librtp/source/rtcp-psfb.s"
	@echo "... librtp/source/rtcp-rr.o"
	@echo "... librtp/source/rtcp-rr.i"
	@echo "... librtp/source/rtcp-rr.s"
	@echo "... librtp/source/rtcp-rtpfb.o"
	@echo "... librtp/source/rtcp-rtpfb.i"
	@echo "... librtp/source/rtcp-rtpfb.s"
	@echo "... librtp/source/rtcp-sdec.o"
	@echo "... librtp/source/rtcp-sdec.i"
	@echo "... librtp/source/rtcp-sdec.s"
	@echo "... librtp/source/rtcp-sr.o"
	@echo "... librtp/source/rtcp-sr.i"
	@echo "... librtp/source/rtcp-sr.s"
	@echo "... librtp/source/rtcp-xr.o"
	@echo "... librtp/source/rtcp-xr.i"
	@echo "... librtp/source/rtcp-xr.s"
	@echo "... librtp/source/rtcp.o"
	@echo "... librtp/source/rtcp.i"
	@echo "... librtp/source/rtcp.s"
	@echo "... librtp/source/rtp-demuxer.o"
	@echo "... librtp/source/rtp-demuxer.i"
	@echo "... librtp/source/rtp-demuxer.s"
	@echo "... librtp/source/rtp-member-list.o"
	@echo "... librtp/source/rtp-member-list.i"
	@echo "... librtp/source/rtp-member-list.s"
	@echo "... librtp/source/rtp-member.o"
	@echo "... librtp/source/rtp-member.i"
	@echo "... librtp/source/rtp-member.s"
	@echo "... librtp/source/rtp-packet.o"
	@echo "... librtp/source/rtp-packet.i"
	@echo "... librtp/source/rtp-packet.s"
	@echo "... librtp/source/rtp-profile.o"
	@echo "... librtp/source/rtp-profile.i"
	@echo "... librtp/source/rtp-profile.s"
	@echo "... librtp/source/rtp-queue.o"
	@echo "... librtp/source/rtp-queue.i"
	@echo "... librtp/source/rtp-queue.s"
	@echo "... librtp/source/rtp-ssrc.o"
	@echo "... librtp/source/rtp-ssrc.i"
	@echo "... librtp/source/rtp-ssrc.s"
	@echo "... librtp/source/rtp-time.o"
	@echo "... librtp/source/rtp-time.i"
	@echo "... librtp/source/rtp-time.s"
	@echo "... librtp/source/rtp.o"
	@echo "... librtp/source/rtp.i"
	@echo "... librtp/source/rtp.s"
	@echo "... librtsp/sdp/sdp-aac.o"
	@echo "... librtsp/sdp/sdp-aac.i"
	@echo "... librtsp/sdp/sdp-aac.s"
	@echo "... librtsp/sdp/sdp-av1.o"
	@echo "... librtsp/sdp/sdp-av1.i"
	@echo "... librtsp/sdp/sdp-av1.s"
	@echo "... librtsp/sdp/sdp-fmtp-load.o"
	@echo "... librtsp/sdp/sdp-fmtp-load.i"
	@echo "... librtsp/sdp/sdp-fmtp-load.s"
	@echo "... librtsp/sdp/sdp-g7xx.o"
	@echo "... librtsp/sdp/sdp-g7xx.i"
	@echo "... librtsp/sdp/sdp-g7xx.s"
	@echo "... librtsp/sdp/sdp-h264.o"
	@echo "... librtsp/sdp/sdp-h264.i"
	@echo "... librtsp/sdp/sdp-h264.s"
	@echo "... librtsp/sdp/sdp-h265.o"
	@echo "... librtsp/sdp/sdp-h265.i"
	@echo "... librtsp/sdp/sdp-h265.s"
	@echo "... librtsp/sdp/sdp-h266.o"
	@echo "... librtsp/sdp/sdp-h266.i"
	@echo "... librtsp/sdp/sdp-h266.s"
	@echo "... librtsp/sdp/sdp-mpeg2.o"
	@echo "... librtsp/sdp/sdp-mpeg2.i"
	@echo "... librtsp/sdp/sdp-mpeg2.s"
	@echo "... librtsp/sdp/sdp-mpeg4.o"
	@echo "... librtsp/sdp/sdp-mpeg4.i"
	@echo "... librtsp/sdp/sdp-mpeg4.s"
	@echo "... librtsp/sdp/sdp-opus.o"
	@echo "... librtsp/sdp/sdp-opus.i"
	@echo "... librtsp/sdp/sdp-opus.s"
	@echo "... librtsp/sdp/sdp-payload.o"
	@echo "... librtsp/sdp/sdp-payload.i"
	@echo "... librtsp/sdp/sdp-payload.s"
	@echo "... librtsp/sdp/sdp-vpx.o"
	@echo "... librtsp/sdp/sdp-vpx.i"
	@echo "... librtsp/sdp/sdp-vpx.s"
	@echo "... librtsp/source/rtsp-header-range.o"
	@echo "... librtsp/source/rtsp-header-range.i"
	@echo "... librtsp/source/rtsp-header-range.s"
	@echo "... librtsp/source/rtsp-header-rtp-info.o"
	@echo "... librtsp/source/rtsp-header-rtp-info.i"
	@echo "... librtsp/source/rtsp-header-rtp-info.s"
	@echo "... librtsp/source/rtsp-header-session.o"
	@echo "... librtsp/source/rtsp-header-session.i"
	@echo "... librtsp/source/rtsp-header-session.s"
	@echo "... librtsp/source/rtsp-header-transport.o"
	@echo "... librtsp/source/rtsp-header-transport.i"
	@echo "... librtsp/source/rtsp-header-transport.s"
	@echo "... librtsp/source/rtsp-media.o"
	@echo "... librtsp/source/rtsp-media.i"
	@echo "... librtsp/source/rtsp-media.s"
	@echo "... librtsp/source/rtsp-multicast.o"
	@echo "... librtsp/source/rtsp-multicast.i"
	@echo "... librtsp/source/rtsp-multicast.s"
	@echo "... librtsp/source/rtsp-reason.o"
	@echo "... librtsp/source/rtsp-reason.i"
	@echo "... librtsp/source/rtsp-reason.s"
	@echo "... librtsp/source/sdp-options.o"
	@echo "... librtsp/source/sdp-options.i"
	@echo "... librtsp/source/sdp-options.s"
	@echo "... librtsp/source/server/rtsp-server-announce.o"
	@echo "... librtsp/source/server/rtsp-server-announce.i"
	@echo "... librtsp/source/server/rtsp-server-announce.s"
	@echo "... librtsp/source/server/rtsp-server-describe.o"
	@echo "... librtsp/source/server/rtsp-server-describe.i"
	@echo "... librtsp/source/server/rtsp-server-describe.s"
	@echo "... librtsp/source/server/rtsp-server-handler.o"
	@echo "... librtsp/source/server/rtsp-server-handler.i"
	@echo "... librtsp/source/server/rtsp-server-handler.s"
	@echo "... librtsp/source/server/rtsp-server-options.o"
	@echo "... librtsp/source/server/rtsp-server-options.i"
	@echo "... librtsp/source/server/rtsp-server-options.s"
	@echo "... librtsp/source/server/rtsp-server-parameter.o"
	@echo "... librtsp/source/server/rtsp-server-parameter.i"
	@echo "... librtsp/source/server/rtsp-server-parameter.s"
	@echo "... librtsp/source/server/rtsp-server-pause.o"
	@echo "... librtsp/source/server/rtsp-server-pause.i"
	@echo "... librtsp/source/server/rtsp-server-pause.s"
	@echo "... librtsp/source/server/rtsp-server-play.o"
	@echo "... librtsp/source/server/rtsp-server-play.i"
	@echo "... librtsp/source/server/rtsp-server-play.s"
	@echo "... librtsp/source/server/rtsp-server-record.o"
	@echo "... librtsp/source/server/rtsp-server-record.i"
	@echo "... librtsp/source/server/rtsp-server-record.s"
	@echo "... librtsp/source/server/rtsp-server-setup.o"
	@echo "... librtsp/source/server/rtsp-server-setup.i"
	@echo "... librtsp/source/server/rtsp-server-setup.s"
	@echo "... librtsp/source/server/rtsp-server-teardown.o"
	@echo "... librtsp/source/server/rtsp-server-teardown.i"
	@echo "... librtsp/source/server/rtsp-server-teardown.s"
	@echo "... librtsp/source/server/rtsp-server.o"
	@echo "... librtsp/source/server/rtsp-server.i"
	@echo "... librtsp/source/server/rtsp-server.s"
	@echo "... librtsp/source/utils/rtp-sender.o"
	@echo "... librtsp/source/utils/rtp-sender.i"
	@echo "... librtsp/source/utils/rtp-sender.s"
	@echo "... librtsp/source/utils/rtsp-demuxer.o"
	@echo "... librtsp/source/utils/rtsp-demuxer.i"
	@echo "... librtsp/source/utils/rtsp-demuxer.s"
	@echo "... librtsp/source/utils/rtsp-muxer.o"
	@echo "... librtsp/source/utils/rtsp-muxer.i"
	@echo "... librtsp/source/utils/rtsp-muxer.s"
	@echo "... mpeg4-aac.o"
	@echo "... mpeg4-aac.i"
	@echo "... mpeg4-aac.s"
	@echo "... mpeg4-avc.o"
	@echo "... mpeg4-avc.i"
	@echo "... mpeg4-avc.s"
	@echo "... mpeg4-bits.o"
	@echo "... mpeg4-bits.i"
	@echo "... mpeg4-bits.s"
	@echo "... mpeg4-hevc.o"
	@echo "... mpeg4-hevc.i"
	@echo "... mpeg4-hevc.s"
	@echo "... mpeg4-vvc.o"
	@echo "... mpeg4-vvc.i"
	@echo "... mpeg4-vvc.s"
	@echo "... rtp-tcp-transport.o"
	@echo "... rtp-tcp-transport.i"
	@echo "... rtp-tcp-transport.s"
	@echo "... rtp-udp-transport.o"
	@echo "... rtp-udp-transport.i"
	@echo "... rtp-udp-transport.s"
	@echo "... rtsp-server-test.o"
	@echo "... rtsp-server-test.i"
	@echo "... rtsp-server-test.s"
	@echo "... rtsp-utils.o"
	@echo "... rtsp-utils.i"
	@echo "... rtsp-utils.s"
	@echo "... sys/base64.o"
	@echo "... sys/base64.i"
	@echo "... sys/base64.s"
	@echo "... sys/ntp-time.o"
	@echo "... sys/ntp-time.i"
	@echo "... sys/ntp-time.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

