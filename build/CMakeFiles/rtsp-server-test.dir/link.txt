/usr/bin/c++  -Wall -Wextra -O2 CMakeFiles/rtsp-server-test.dir/rtsp-server-test.cpp.o CMakeFiles/rtsp-server-test.dir/h264-file-source.cpp.o CMakeFiles/rtsp-server-test.dir/h264-file-reader.cpp.o CMakeFiles/rtsp-server-test.dir/h265-file-source.cpp.o CMakeFiles/rtsp-server-test.dir/h265-file-reader.cpp.o CMakeFiles/rtsp-server-test.dir/rtp-udp-transport.cpp.o CMakeFiles/rtsp-server-test.dir/rtp-tcp-transport.cpp.o CMakeFiles/rtsp-server-test.dir/rtsp-utils.cpp.o CMakeFiles/rtsp-server-test.dir/http-reason.c.o CMakeFiles/rtsp-server-test.dir/sys/ntp-time.c.o CMakeFiles/rtsp-server-test.dir/mpeg4-bits.c.o CMakeFiles/rtsp-server-test.dir/mpeg4-avc.c.o CMakeFiles/rtsp-server-test.dir/mpeg4-hevc.c.o CMakeFiles/rtsp-server-test.dir/mpeg4-vvc.c.o CMakeFiles/rtsp-server-test.dir/mpeg4-aac.c.o CMakeFiles/rtsp-server-test.dir/aom-av1.c.o CMakeFiles/rtsp-server-test.dir/http-header-auth.c.o CMakeFiles/rtsp-server-test.dir/http-parser.c.o CMakeFiles/rtsp-server-test.dir/sys/base64.c.o -o rtsp-server-test  librtp.a -lpthread /usr/lib/x86_64-linux-gnu/libpthread.a 
