# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/rtspSRV

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/rtspSRV/build

# Include any dependencies generated for this target.
include CMakeFiles/rtp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/rtp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/rtp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/rtp.dir/flags.make

CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o: ../librtp/payload/rtp-av1-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-av1-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-av1-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-av1-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o: ../librtp/payload/rtp-av1-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-av1-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-av1-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-av1-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o: ../librtp/payload/rtp-h264-bitstream.c
CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-h264-bitstream.c

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-h264-bitstream.c > CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-h264-bitstream.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o: ../librtp/payload/rtp-h264-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-h264-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-h264-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-h264-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o: ../librtp/payload/rtp-h264-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-h264-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-h264-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-h264-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o: ../librtp/payload/rtp-h265-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-h265-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-h265-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-h265-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o: ../librtp/payload/rtp-h265-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-h265-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-h265-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-h265-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o: ../librtp/payload/rtp-h266-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-h266-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-h266-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-h266-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o: ../librtp/payload/rtp-h266-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-h266-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-h266-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-h266-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o: ../librtp/payload/rtp-mp4a-latm-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o: ../librtp/payload/rtp-mp4a-latm-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mp4a-latm-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o: ../librtp/payload/rtp-mp4v-es-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o: ../librtp/payload/rtp-mp4v-es-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mp4v-es-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o: ../librtp/payload/rtp-mpeg1or2es-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o: ../librtp/payload/rtp-mpeg1or2es-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg1or2es-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o: ../librtp/payload/rtp-mpeg4-generic-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o: ../librtp/payload/rtp-mpeg4-generic-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-mpeg4-generic-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o: ../librtp/payload/rtp-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o: ../librtp/payload/rtp-payload-helper.c
CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-payload-helper.c

CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-payload-helper.c > CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-payload-helper.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o: ../librtp/payload/rtp-payload.c
CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-payload.c

CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-payload.c > CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-payload.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o: ../librtp/payload/rtp-ps-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-ps-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-ps-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-ps-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o: ../librtp/payload/rtp-ts-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-ts-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-ts-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-ts-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o: ../librtp/payload/rtp-ts-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-ts-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-ts-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-ts-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o: ../librtp/payload/rtp-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o: ../librtp/payload/rtp-vp8-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-vp8-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-vp8-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-vp8-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o: ../librtp/payload/rtp-vp8-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-vp8-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-vp8-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-vp8-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o: ../librtp/payload/rtp-vp9-pack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-vp9-pack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-vp9-pack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-vp9-pack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.s

CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o: ../librtp/payload/rtp-vp9-unpack.c
CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o -MF CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o.d -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o -c /home/<USER>/rtspSRV/librtp/payload/rtp-vp9-unpack.c

CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/payload/rtp-vp9-unpack.c > CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.i

CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/payload/rtp-vp9-unpack.c -o CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o: ../librtp/source/rtcp-app.c
CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-app.c

CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-app.c > CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-app.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o: ../librtp/source/rtcp-bye.c
CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-bye.c

CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-bye.c > CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-bye.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o: ../librtp/source/rtcp-interval.c
CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-interval.c

CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-interval.c > CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-interval.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o: ../librtp/source/rtcp-psfb.c
CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-psfb.c

CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-psfb.c > CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-psfb.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o: ../librtp/source/rtcp-rr.c
CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-rr.c

CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-rr.c > CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-rr.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o: ../librtp/source/rtcp-rtpfb.c
CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-rtpfb.c

CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-rtpfb.c > CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-rtpfb.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o: ../librtp/source/rtcp-sdec.c
CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-sdec.c

CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-sdec.c > CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-sdec.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o: ../librtp/source/rtcp-sr.c
CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-sr.c

CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-sr.c > CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-sr.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o: ../librtp/source/rtcp-xr.c
CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp-xr.c

CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp-xr.c > CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp-xr.c -o CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.s

CMakeFiles/rtp.dir/librtp/source/rtcp.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtcp.c.o: ../librtp/source/rtcp.c
CMakeFiles/rtp.dir/librtp/source/rtcp.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object CMakeFiles/rtp.dir/librtp/source/rtcp.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtcp.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtcp.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtcp.c.o -c /home/<USER>/rtspSRV/librtp/source/rtcp.c

CMakeFiles/rtp.dir/librtp/source/rtcp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtcp.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtcp.c > CMakeFiles/rtp.dir/librtp/source/rtcp.c.i

CMakeFiles/rtp.dir/librtp/source/rtcp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtcp.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtcp.c -o CMakeFiles/rtp.dir/librtp/source/rtcp.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o: ../librtp/source/rtp-demuxer.c
CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-demuxer.c

CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-demuxer.c > CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-demuxer.c -o CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o: ../librtp/source/rtp-member-list.c
CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-member-list.c

CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-member-list.c > CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-member-list.c -o CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o: ../librtp/source/rtp-member.c
CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-member.c

CMakeFiles/rtp.dir/librtp/source/rtp-member.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-member.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-member.c > CMakeFiles/rtp.dir/librtp/source/rtp-member.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-member.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-member.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-member.c -o CMakeFiles/rtp.dir/librtp/source/rtp-member.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o: ../librtp/source/rtp-packet.c
CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-packet.c

CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-packet.c > CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-packet.c -o CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o: ../librtp/source/rtp-profile.c
CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-profile.c

CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-profile.c > CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-profile.c -o CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o: ../librtp/source/rtp-queue.c
CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-queue.c

CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-queue.c > CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-queue.c -o CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o: ../librtp/source/rtp-ssrc.c
CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-ssrc.c

CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-ssrc.c > CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-ssrc.c -o CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.s

CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o: ../librtp/source/rtp-time.c
CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp-time.c

CMakeFiles/rtp.dir/librtp/source/rtp-time.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp-time.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp-time.c > CMakeFiles/rtp.dir/librtp/source/rtp-time.c.i

CMakeFiles/rtp.dir/librtp/source/rtp-time.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp-time.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp-time.c -o CMakeFiles/rtp.dir/librtp/source/rtp-time.c.s

CMakeFiles/rtp.dir/librtp/source/rtp.c.o: CMakeFiles/rtp.dir/flags.make
CMakeFiles/rtp.dir/librtp/source/rtp.c.o: ../librtp/source/rtp.c
CMakeFiles/rtp.dir/librtp/source/rtp.c.o: CMakeFiles/rtp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building C object CMakeFiles/rtp.dir/librtp/source/rtp.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rtp.dir/librtp/source/rtp.c.o -MF CMakeFiles/rtp.dir/librtp/source/rtp.c.o.d -o CMakeFiles/rtp.dir/librtp/source/rtp.c.o -c /home/<USER>/rtspSRV/librtp/source/rtp.c

CMakeFiles/rtp.dir/librtp/source/rtp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rtp.dir/librtp/source/rtp.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/rtspSRV/librtp/source/rtp.c > CMakeFiles/rtp.dir/librtp/source/rtp.c.i

CMakeFiles/rtp.dir/librtp/source/rtp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rtp.dir/librtp/source/rtp.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/rtspSRV/librtp/source/rtp.c -o CMakeFiles/rtp.dir/librtp/source/rtp.c.s

# Object files for target rtp
rtp_OBJECTS = \
"CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o" \
"CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtcp.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o" \
"CMakeFiles/rtp.dir/librtp/source/rtp.c.o"

# External object files for target rtp
rtp_EXTERNAL_OBJECTS =

librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-av1-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-av1-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-h264-bitstream.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-h264-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-h264-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-h265-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-h265-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-h266-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-h266-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mp4a-latm-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mp4v-es-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg1or2es-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-mpeg4-generic-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-payload-helper.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-payload.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-ps-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-ts-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-ts-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-vp8-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-pack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/payload/rtp-vp9-unpack.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-app.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-bye.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-interval.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-psfb.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-rr.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-rtpfb.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-sdec.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-sr.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp-xr.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtcp.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-demuxer.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-member-list.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-member.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-packet.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-profile.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-queue.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-ssrc.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp-time.c.o
librtp.a: CMakeFiles/rtp.dir/librtp/source/rtp.c.o
librtp.a: CMakeFiles/rtp.dir/build.make
librtp.a: CMakeFiles/rtp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/rtspSRV/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Linking C static library librtp.a"
	$(CMAKE_COMMAND) -P CMakeFiles/rtp.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/rtp.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/rtp.dir/build: librtp.a
.PHONY : CMakeFiles/rtp.dir/build

CMakeFiles/rtp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/rtp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/rtp.dir/clean

CMakeFiles/rtp.dir/depend:
	cd /home/<USER>/rtspSRV/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/rtspSRV /home/<USER>/rtspSRV /home/<USER>/rtspSRV/build /home/<USER>/rtspSRV/build /home/<USER>/rtspSRV/build/CMakeFiles/rtp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/rtp.dir/depend

