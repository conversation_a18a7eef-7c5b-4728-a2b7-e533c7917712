#include "http-reason.h"

// HTTP status code to reason phrase mapping
const char* http_reason_phrase(int code)
{
    switch (code) {
        // 1xx Informational
        case 100: return "Continue";
        case 101: return "Switching Protocols";
        
        // 2xx Success
        case 200: return "OK";
        case 201: return "Created";
        case 202: return "Accepted";
        case 204: return "No Content";
        case 206: return "Partial Content";
        
        // 3xx Redirection
        case 300: return "Multiple Choices";
        case 301: return "Moved Permanently";
        case 302: return "Found";
        case 304: return "Not Modified";
        case 307: return "Temporary Redirect";
        
        // 4xx Client Error
        case 400: return "Bad Request";
        case 401: return "Unauthorized";
        case 403: return "Forbidden";
        case 404: return "Not Found";
        case 405: return "Method Not Allowed";
        case 406: return "Not Acceptable";
        case 408: return "Request Timeout";
        case 410: return "Gone";
        case 412: return "Precondition Failed";
        case 413: return "Request Entity Too Large";
        case 414: return "Request-URI Too Large";
        case 415: return "Unsupported Media Type";
        case 451: return "Parameter Not Understood";
        case 452: return "Conference Not Found";
        case 453: return "Not Enough Bandwidth";
        case 454: return "Session Not Found";
        case 455: return "Method Not Valid in This State";
        case 456: return "Header Field Not Valid for Resource";
        case 457: return "Invalid Range";
        case 458: return "Parameter Is Read-Only";
        case 459: return "Aggregate operation not allowed";
        case 460: return "Only aggregate operation allowed";
        case 461: return "Unsupported transport";
        case 462: return "Destination unreachable";
        
        // 5xx Server Error
        case 500: return "Internal Server Error";
        case 501: return "Not Implemented";
        case 502: return "Bad Gateway";
        case 503: return "Service Unavailable";
        case 504: return "Gateway Timeout";
        case 505: return "RTSP Version not supported";
        case 551: return "Option not supported";
        
        default: return "Unknown";
    }
}
